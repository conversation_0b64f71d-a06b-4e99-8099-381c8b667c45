import { StorageSerializers, useStorage } from "@vueuse/core"

// Composable to manage selected publish channel with persistent storage
export const useSelectedPubChannel = () => useStorage("selected-pub-channel", "global")

// Composable to manage greeting screen
export const useGreetingObject = () =>
	useStorage("greeting-object", {
		titleSize: 36,
		listSize: 20,
		guestSize: 24,
		titleFont: "Abril Fatface",
		listFont: "Abril Fatface",
		guestFont: "Abril Fatface"
	})

export const useLastPrinter = () =>
	useStorage("last-printer", null, undefined, { serializer: StorageSerializers.object })
