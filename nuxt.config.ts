import { pagesExtend } from "./pages-extend"
import gql from "@rollup/plugin-graphql"
import { directus } from "./config"
import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import { NaiveUiResolver } from "unplugin-vue-components/resolvers"

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
				devtools: {
								enabled: true,
								timeline: {
												enabled: true
								}
				},

				sourcemap: false,

				// ssr: false,

				modules: [
								"@pinia/nuxt",
								"@pinia-plugin-persistedstate/nuxt",
								"@nuxtjs/i18n",
								"@nuxtjs/device",
								"nuxt-svgo",
								"@nuxtjs/tailwindcss",
								"@nuxt/test-utils/module",
								"nuxtjs-naive-ui",
								"v-wave/nuxt",
								"nuxt-viewport",
								"nuxt-directus",
								"@vueuse/nuxt",
								"@formkit/auto-animate/nuxt",
								"nuxt-lodash"
				],

				directus,

				app: {
								rootId: "app",
								head: {
												charset: "utf-8",
												viewport: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
								}
				},

				runtimeConfig: {
								public: {
												directus: {
																url: process.env.DIRECTUS_URL
												},
												directusUrl: process.env.DIRECTUS_URL
								},
								private: {
												directusUrl: process.env.DIRECTUS_URL,
												graphqlEndpoint: process.env.GRAPHQL_ENDPOINT,
												wsGraphqlEndpoint: process.env.WS_GRAPHQL_ENDPOINT,
												adminToken: process.env.ADMIN_TOKEN,
												ablyToken: process.env.ABLY_TOKEN
												// USE LIKE THIS:
												// this.$config.directusUrl
												// this.$config.graphqlEndpoint
												// this.$config.wsGraphqlEndpoint
								},
								googleFontsApiKey: process.env.GOOGLE_FONTS_API_KEY
				},

				components: [
								{
												path: "~/components/common",
												pathPrefix: false
								},
								{
												path: "~/components/app",
												pathPrefix: false
								}
				],

				svgo: {
								defaultImport: "component"
				},

				i18n: {
								vueI18n: "./i18n.config.ts"
				},

				build: {
								transpile:
												process.env.NODE_ENV === "production"
																? ["naive-ui", "vueuc", "@css-render/vue3-ssr", "@juggle/resize-observer"]
																: ["@juggle/resize-observer"]
				},

				vite: {
								vue: {
												script: {
																defineModel: true,
																propsDestructure: true
												}
								},
								optimizeDeps: {
												include:
																process.env.NODE_ENV === "development"
																				? ["naive-ui", "vueuc", "date-fns-tz/formatInTimeZone", "fast-deep-equal"]
																				: ["fast-deep-equal"]
								},
								plugins: [
												gql(),
												AutoImport({
																imports: [
																				{
																								"naive-ui": ["useDialog", "useMessage", "useNotification", "useLoadingBar"]
																				}
																]
												}),
												Components({
																resolvers: [NaiveUiResolver()]
												})
								]
				},

				hooks: {
								"pages:extend": pages => pagesExtend(pages)
				},

				compatibilityDate: "2024-07-07"
})