<template>
	<div>
		<n-space vertical size="large" class="mt-5">
			<n-form label-placement="left" require-mark-placement="right-hanging" label-width="auto">
				<n-form-item label="Cari Tamu">
					<n-input
						v-model:value="fetchParam.search"
						class="mr-4"
						placeholder="Cari Tamu"
						clearable
						@keyup.enter="fetchGuests"
						@input="delayedSearch"
						@clear="handleClear"
					/>
					<n-button circle type="primary" :ghost="!showOptions" @click="showOptions = !showOptions">
						<template #icon>
							<Icon name="ion:filter" />
						</template>
					</n-button>
				</n-form-item>
				<n-form-item label="Template">
					<n-select
						v-model:value="selectedTemplate"
						:options="options"
						placeholder="Pilih Template"
						style="width: 100%"
					/>
				</n-form-item>
				<!-- <n-form-item>
					<n-space>
						<n-button @click="showModal = true">preview template</n-button>
						<n-button circle type="primary" :ghost="!showOptions" @click="showOptions = !showOptions">
							<template #icon>
								<Icon name="ion:filter" />
							</template>
						</n-button>
					</n-space>
				</n-form-item> -->
			</n-form>
			<n-collapse-transition :show="showOptions">
				<SenderGuestFilter :agenda="props.agenda" @change="handleFilterChange" />
			</n-collapse-transition>
			<template v-if="guestsData">
				<n-data-table
					size="small"
					:loading="loadingGuest"
					:columns="columns"
					:data="guestsData"
					:row-key="rowKey"
					:checked-row-keys="checkedRowKeys"
					@update:checked-row-keys="handleCheck"
				/>
				<n-space align="center">
					<n-pagination
						v-model:page="fetchParam.page"
						:page-count="totalPages"
						:page-slot="7"
						@update:page="fetchGuests"
					/>
					<n-select
						v-model:value="fetchParam.limit"
						:options="optionsRowPerPage"
						@update:value="handleChangeRowPerPage"
					/>
				</n-space>
			</template>
		</n-space>
	</div>
</template>

<script setup>
import {
	NSpace,
	NP,
	NSelect,
	NInput,
	useLoadingBar,
	useMessage,
	NDataTable,
	NButton,
	NForm,
	NFormItem,
	NPagination,
	NCollapseTransition,
	NCheckbox
} from "naive-ui"
import Icon from "~/components/common/Icon.vue"

const props = defineProps({
	agenda: Object
})

const loadingBar = useLoadingBar()
const message = useMessage()
const viewport = useViewport()
const { updateItem } = useDirectusItems()
const route = useRoute()

const { data: guestsData, meta: guestsMeta, load: loadGuests, loading: loadingGuest } = useGetGuests()
const fetchParam = reactive({
	filter: {
		_and: [
			{
				event: {
					_eq: route.params.id
				}
			}
			/* {
				phone: {
					_nnull: true
				}
			},
			{
				phone: {
					_nempty: true
				}
			} */
		]
	},
	sort: ["-date_created", "-date_updated"],
	search: "",
	fields: ["*"],
	meta: "*",
	limit: 25,
	page: 1
})

const fetchGuests = async () => {
	loadingBar.start()
	try {
		await loadGuests(fetchParam)
		// await getAggregate()
		loadingBar.finish()
	} catch (e) {
		loadingBar.error()
		console.error(e.message)
		message.error(e.message)
	} finally {
		loadingGuest.value = false
	}
}

const totalPages = computed(() => {
	return Math.ceil(guestsMeta.value.filter_count / fetchParam.limit)
})

const handleChangeRowPerPage = async () => {
	fetchParam.page = 1
	await fetchGuests()
}

const showOptions = ref(false)

const delayedSearch = useDebounceFn(async () => {
	fetchParam.page = 1
	await fetchGuests()
}, 500)
const handleClear = async () => {
	fetchParam.search = ""
	fetchParam.page = 1
	await fetchGuests()
}

const handleFilterChange = async filterParam => {
	fetchParam.filter = filterParam
	await delayedSearch()
}

const checkedRowKeys = ref([]) // Define checkedRowKeys
const rowKey = row => row.id
const handleCheck = rowKeys => {
	checkedRowKeys.value = rowKeys
}
const columns = [
	/* {
		type: "selection"
	}, */
	{
		title: "Nama",
		key: "name",
		ellipsis: {
			tooltip: true
		},
		render(row) {
			return [
				h(
					NP,
					{
						depth: 1,
						class: "m-0 p-0"
					},
					{ default: () => row.name }
				),
				h(
					NP,
					{
						depth: 3,
						class: "m-0 p-0 text-xs"
					},
					{ default: () => row.status_relation }
				)
			]
		}
	},
	{
		title: "Aksi",
		key: "actions",
		align: "left",
		fixed: "right",
		width: 120,
		render(row) {
			return h(
				NSpace,
				{
					size: "large"
				},
				{
					default: () => [
						h(NCheckbox, {
							checked: row.sent,
							onClick: () => {
								updateSent(row)
							}
						}),
						h(
							NButton,
							{
								size: "medium",
								text: true,
								type: "default",
								onClick: () => copyTemplate(row)
							},
							{
								icon: () =>
									h(Icon, {
										name: "ion:copy-outline"
									})
							}
						),
						h(
							NButton,
							{
								size: "medium",
								text: true,
								type: "info",
								onClick: () => send(row)
							},
							{
								icon: () =>
									h(Icon, {
										name: "logos:whatsapp-icon"
									})
							}
						)
					]
				}
			)
		}
	}
]

const updateSent = async row => {
	try {
		await updateItem({
			collection: "guest",
			id: row.id,
			item: {
				...row,
				sent: row.sent ? false : true
			}
		})
		row.sent = !row.sent
	} catch (e) {
		console.log(e)
	}
}

const showModal = ref(false)
const initTemplate = `

Assalamu'alaikum Wr. Wb.

Dengan penuh rasa syukur kepada Allah SWT, kami mengundang Bapak/Ibu/Saudara/i *[nama-tamu]* untuk menghadiri acara pernikahan kami:

[event]

Yang InsyaAllah akan dilaksanakan pada:

📅 Hari/Tanggal: [tanggal]
⏰ Waktu: [sesi]
🏛️ Tempat: [tempat]
🚩 meja: [meja]

link:
[link]?qr=[qr]

Merupakan suatu kehormatan dan kebahagiaan bagi kami apabila Bapak/Ibu/Saudara/i dapat hadir untuk memberikan doa restu pada acara kami.

Wassalamu'alaikum Wr. Wb.

Hormat Kami
`
const template = ref(null)

const changeSpacetoPlus = text => {
	return text.replaceAll(" ", "+")
}

const composeTemplate = row => {
	let templateValue = getContentById(selectedTemplate.value)
	templateValue = templateValue.replaceAll("[qr]", encodeURI(row.code_guest) || "")
	templateValue = templateValue.replaceAll("[nama-tamu]", row.name || "")
	templateValue = templateValue.replaceAll("[status]", row.status_relation || "")
	templateValue = templateValue.replaceAll("[sesi]", row.shift || "")
	templateValue = templateValue.replaceAll("[meja]", row.table || "")
	templateValue = templateValue.replaceAll("[level]", translateLevel(row.level))
	templateValue = templateValue.replaceAll("[event]", props.agenda.title)
	templateValue = templateValue.replaceAll("[tanggal]", props.agenda.start || "")
	templateValue = templateValue.replaceAll("[link]", props.agenda.url_post || "")
	templateValue = templateValue.replaceAll("[tempat]", props.agenda.location || "")
	templateValue = templateValue.replaceAll("[url-nama-tamu]", encodeURI(row.name) || "")
	templateValue = templateValue.replaceAll("[link-nama-tamu]", changeSpacetoPlus(row.name) || "")

	return templateValue
}

const send = async row => {
	if (selectedTemplate.value == null) {
		message.error("Pilih template terlebih dahulu")
		return
	}
	try {
		await updateItem({
			collection: "guest",
			id: row.id,
			item: {
				...row,
				sent: row.sent ? false : true
			}
		})
		row.sent = true
	} catch (e) {
		console.log(e)
	}
	let templateValue = composeTemplate(row)
	templateValue = encodeURIComponent(templateValue)

	let url = `https://api.whatsapp.com/send?phone=${row.phone ?? ""}&text=${templateValue}`
	window.open(url, "_blank")
}

const { text, copy, copied, isSupported } = useClipboard({ template })

const copyTemplate = row => {
	if (selectedTemplate.value == null) {
		message.error("Pilih template terlebih dahulu")
		return
	}
	let templateValue = composeTemplate(row)
	copy(templateValue)
	message.success("template tersalin ke clipboard")
}

const { data: templatesData, meta: templatesMeta, load: loadTemplates, loading: loadingTemplates } = useGetTemplates()

const options = ref([{ value: 0, label: "default", content: initTemplate }])

const getContentById = id => {
	const item = options.value.find(i => i.value === id)
	return item ? item.content : null
}

const selectedTemplate = ref(null)

onMounted(async () => {
	await fetchGuests()
	const fetchParam = {
		filter: {
			event: {
				_eq: props.agenda.id
			}
		}
	}

	await loadTemplates(fetchParam)
	if (templatesData.value?.length > 0) {
		templatesData.value?.forEach(template => {
			options.value.push({
				value: template.id,
				label: template.title,
				content: template.content
			})
		})
	}
})
</script>

<style></style>
