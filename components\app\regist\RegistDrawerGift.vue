<template>
	<CustomDrawer v-model:show="show" title="Titip" @close="handleClose">
		<n-form ref="formGuestRef" :model="guestModel">
			<template v-for="(item, index) in guestModel.entrusts" :key="'titipan-' + index">
				<n-card class="mb-3" :title="'Titipan ' + (index + 1)" closable @close="removeItem(index)">
					<n-form-item
						ignore-path-change
						label="Nama"
						:path="`entrusts.${index}.name`"
						:rule="{
							required: true,
							message: 'Nama tidak boleh kosong',
							trigger: ['input', 'blur']
						}"
					>
						<n-input v-model:value="item.name" placeholder="Masukkan nama tamu" @keydown.enter.prevent />
					</n-form-item>
					<n-form-item
						ignore-path-change
						label="Hadiah"
						:path="`entrusts.${index}.gift`"
						:rule="{
							type: 'array',
							required: true,
							trigger: ['input', 'blur', 'change'],
							message: '<PERSON><PERSON>h hadiah'
						}"
					>
						<n-checkbox-group v-model:value="item.gift">
							<n-space item-style="display: flex;">
								<n-checkbox value="1" label="Cash" />
								<n-checkbox value="2" label="Kado" />
								<n-checkbox value="3" label="Transfer" />
							</n-space>
						</n-checkbox-group>
					</n-form-item>
					<n-form-item label="Souvenir diberikan">
						<n-switch v-model:value="item.take_souvenir" />
					</n-form-item>
					<n-form-item v-if="agenda?.print_feature && !isIos">
						<n-button class="w-full mb-4" @click="printEntrust(index)">
							<template #icon>
								<Icon name="carbon:printer" />
							</template>
							Cetak
						</n-button>
					</n-form-item>
				</n-card>
			</template>
			<n-form-item>
				<n-button block @click="addEntrust">
					<template #icon>
						<Icon name="carbon:gift" />
					</template>
					Tambah titipan
				</n-button>
			</n-form-item>
		</n-form>
		<template #footer>
			<n-button block :loading="submitLoading" type="primary" @click="handleSubmit">Konfirmasi</n-button>
		</template>
	</CustomDrawer>
</template>

<script setup>
import {
	NForm,
	NFormItem,
	NCard,
	NCheckboxGroup,
	NSpace,
	NCheckbox,
	NInput,
	NSwitch,
	NButton,
	useLoadingBar,
	useNotification
} from "naive-ui"
import CustomDrawer from "@/components/app/CustomDrawer.vue"
import ShortUniqueId from "short-unique-id"

const props = defineProps({
	agenda: Object
})

const emit = defineEmits(["close", "after-submit", "print-entrust"])

const show = defineModel()
const { randomUUID } = new ShortUniqueId({ length: 4 })

const loadingBar = useLoadingBar()
const notification = useNotification()
const { createItems } = useDirectusItems()
const user = useDirectusUser()
const { isIos } = useDevice()

let guestModel = reactive({
	entrusts: [
		{
			name: null,
			code_guest: null,
			gift: [],
			presence: false,
			amount_guest: 1,
			take_souvenir: false,
			amount_souvenir: 1,
			entrust: true,
			event: props.agenda.id,
			regist_by: user.value.id
		}
	]
})

const addEntrust = () => {
	guestModel.entrusts.push({
		name: null,
		code_guest: null,
		gift: [],
		presence: false,
		amount_guest: 1,
		take_souvenir: false,
		amount_souvenir: 1,
		entrust: true,
		event: props.agenda.id,
		regist_by: user.value.id
	})
}

const resetModel = () => {
	guestModel = {
		entrusts: [
			{
				name: null,
				code_guest: null,
				gift: [],
				presence: false,
				amount_guest: 1,
				take_souvenir: false,
				amount_souvenir: 1,
				entrust: true,
				event: props.agenda.id,
				regist_by: user.value.id
			}
		]
	}
}

const removeItem = index => {
	guestModel.entrusts.splice(index, 1)
}

const formGuestRef = ref(null)

const submitLoading = ref(false)

const handleSubmit = e => {
	e.preventDefault()
	formGuestRef.value?.validate(async errors => {
		if (!errors) {
			await submitGuest()
		} else {
			console.error(errors)
		}
	})
}

const submitGuest = async () => {
	submitLoading.value = true
	loadingBar.start()
	try {
		guestModel.entrusts.forEach((item, index) => {
			guestModel.entrusts[index].code_guest = props.agenda.code_event + "-gift-" + randomUUID()
		})
		await createItems({ collection: "guest", items: guestModel.entrusts })
		emit("after-submit")
		loadingBar.finish()
		notification.success({
			content: `data tersimpan`,
			description: `Sukses`,
			duration: 2000
		})
		resetModel()
	} catch (e) {
		console.error(e)
		if (e.errors) {
			// message.error(e.errors[0].message)
			notification.error({
				content: `${e.errors[0].message}`,
				description: `Error`,
				duration: 2000
			})
		} else {
			// message.error("gagal submit")
			notification.error({
				content: `gagal konfirmasi`,
				description: `Error`,
				duration: 2000
			})
		}
		loadingBar.error()
	} finally {
		submitLoading.value = false
	}
}

const linkPrintParam = (title, name) => {
	if (!title || !name) return
	const _title = encodeURIComponent(title)
	const _name = encodeURIComponent(name)
	const _qrvalue = encodeURIComponent(`on-site-${title}`)

	return `my.bluetoothprint.scheme://https://app.bagimomen.my.id/api/print-guest?name=${_name}&title=${_title}&qrvalue=${_qrvalue}`
}

const printEntrust = index => {
	emit("print-entrust", guestModel.entrusts[index])
}

const handleClose = () => {
	emit("close")
}
</script>

<style lang="scss" scoped>
::v-deep(.n-input__suffix) {
	gap: 8px;
}
</style>
