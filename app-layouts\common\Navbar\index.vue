<template>
	<nav class="nav" :class="[{ collapsed }, mode]">
		<n-menu
			ref="menu"
			:options="menuOptions"
			:collapsed="collapsed"
			:mode="mode"
			:accordion="true"
			:collapsed-width="collapsedWidth"
			:dropdown-props="{
				scrollable: true,
				menuProps: () => ({
					class: 'main-nav'
				})
			}"
			v-model:value="selectedKey"
			:expanded-keys="expandedKeys"
			@update:expanded-keys="handleUpdateExpandedKeys"
		/>
	</nav>
</template>

<script setup>
import { NMenu } from "naive-ui"
// import getItems from "./items"
import { useThemeStore } from "@/stores/theme"
import { computed, onBeforeMount, ref, toRefs } from "vue"
import _uniq from "lodash/uniq"
import { RouterLink } from "vue-router"

defineOptions({
	name: "Navbar"
})

const route = useRoute()
const router = useRouter()
const user = useDirectusUser()

const props = defineProps({
	mode: {
		type: String,
		default: "vertical",
		validator(value) {
			return ["vertical", "horizontal"].includes(value)
		}
	},
	collapsed: {
		type: Boolean,
		default: false
	}
})

const { mode, collapsed } = toRefs(props)

const selectedKey = ref(null)
const menu = ref(null)
const expandedKeys = ref(undefined)

const themeStore = useThemeStore()

const HomeIcon = "carbon:home"
// const EventIcon = "carbon:event";
const DashboardIcon = "carbon:dashboard"
const GuestsIcon = "carbon:user-multiple"
const RegistIcon = "carbon:mobile-check"
const ScreenIcon = "carbon:screen"
const SenderIcon = "carbon:send-alt"
const CardIcon = "jam:id-card-f"
const PrintIcon = "carbon:printer"
const ImageIcon = "carbon:image"

const menuOptions = computed(() => {
	let id = 0
	if (route.params.id) {
		id = route.params.id
	}

	let baseOptions = [
		// {
		//   label: () =>
		//     h(
		//       RouterLink,
		//       {
		//         to: {
		//           name: "Home",
		//         },
		//       },
		//       { default: () => "Home" }
		//     ),
		//   key: "Home",
		//   icon: renderIcon(HomeIcon),
		//   show: true,
		// },
		// {
		//   label: () =>
		//     h(
		//       RouterLink,
		//       {
		//         to: "/events",
		//       },
		//       { default: () => "Events" }
		//     ),
		//   key: "Events",
		//   icon: renderIcon(EventIcon),
		//   show: true,
		// },
		{
			label: () =>
				h(
					RouterLink,
					{
						to: "/events"
					},
					{ default: () => "Acara" }
				),
			key: "Events",
			icon: renderIcon(HomeIcon),
			show: true
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: `/events/${id}/dashboard`
					},
					{ default: () => "Dashboard" }
				),
			key: "Dashboard",
			icon: renderIcon(DashboardIcon),
			show: false
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: `/events/${id}/guests`
					},
					{ default: () => "Kelola Tamu" }
				),
			key: "Guests",
			icon: renderIcon(GuestsIcon),
			show: false
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: `/events/${id}/sender`
					},
					{ default: () => "Kirim Undangan" }
				),
			key: "Sender",
			icon: renderIcon(SenderIcon),
			show: false
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: `/events/${id}/card`
					},
					{ default: () => "Kartu Undangan" }
				),
			key: "Card",
			icon: renderIcon(CardIcon),
			show: false
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: `/events/${id}/gallery`
					},
					{ default: () => "Galeri" }
				),
			key: "Gallery",
			icon: renderIcon(ImageIcon),
			show: false
		}
	]
	if (user.value.role.name !== "user") {
		baseOptions = [
			...baseOptions,
			{
				label: () =>
					h(
						RouterLink,
						{
							to: `/events/${id}/print`
						},
						{ default: () => "Cetak" }
					),
				key: "Print",
				icon: renderIcon(PrintIcon),
				show: false
			},
			{
				label: () =>
					h(
						RouterLink,
						{
							to: `/events/${id}/registration`
						},
						{ default: () => "Registrasi Tamu" }
					),
				key: "Registration",
				icon: renderIcon(RegistIcon),
				show: false
			},
			{
				label: () =>
					h(
						RouterLink,
						{
							to: `/events/${id}/greetings`
						},
						{ default: () => "Layar Sapa" }
					),
				key: "Greetings",
				icon: renderIcon(ScreenIcon),
				show: false
			}
		]
	}
	// Dynamically control 'show' based on route params
	if (route.params.id) {
		// If params.id exists, all items should be shown
		baseOptions.forEach(option => (option.show = true))
	}
	return baseOptions
})

const collapsedWidth = computed(() => themeStore.sidebar.closeWidth)
const sidebarCollapsed = computed(() => themeStore.sidebar.collapsed)

function setMenuKey(matched) {
	for (const match of matched) {
		if (match.name && typeof match.name === "string") {
			selectedKey.value = match.name?.toString() || null
			if (selectedKey.value) {
				menu.value?.showOption(selectedKey.value)
			}
		}
	}
}

onBeforeMount(() => {
	setMenuKey(route.matched)

	router.afterEach(route => {
		if (route?.matched?.length) {
			setMenuKey(route.matched)

			if (window.innerWidth <= 700 && !sidebarCollapsed.value) {
				themeStore.closeSidebar()
			}
		}
	})
})

// handler to simulate the accordion behavior in a specific submenu
function handleUpdateExpandedKeys(value) {
	const submenu = "components"

	if (value?.length && value.includes(submenu)) {
		const lastKey = value.pop()
		if (lastKey) {
			expandedKeys.value = _uniq([submenu, lastKey])
		}
	} else {
		expandedKeys.value = undefined
	}
}
</script>

<style lang="scss" scoped>
.nav {
	&.collapsed {
		pointer-events: none;
	}

	:deep() {
		.n-menu-item-content,
		.n-menu-item-group {
			.item-badge {
				display: flex;
				justify-content: space-between;
				align-items: center;
				gap: 10px;

				:nth-child(1) {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				:nth-child(2) {
					color: var(--fg-color);
					background: var(--hover-005-color);
					margin-right: 10px;
					height: 22px;
					line-height: 24px;
					border-radius: 15px;
					padding: 0 7px;
					font-weight: bold;
					font-size: 13px;
					font-family: var(--font-family-mono);
				}
			}

			&.n-menu-item-content--selected,
			&.n-menu-item-content--child-active {
				.item-badge {
					:nth-child(2) {
						color: var(--n-item-text-color-active);
						background: var(--n-item-color-active);
					}
				}
			}
		}

		.n-menu-item-group {
			.n-menu-item-group-title {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.item-badge {
				:nth-child(2) {
					font-size: 10px;
					margin-right: 0px;
					height: 20px;
					line-height: 20px;
					border-radius: 8px;
					padding: 0 6px;
				}
			}
		}

		.n-submenu-children {
			.n-menu-item-content {
				&::after {
					content: "";
					display: block;
					opacity: 0.1;
					background-color: var(--fg-color);
					width: 12px;
					height: 2px;
					position: absolute;
					top: 50%;
					left: 38px;
					margin-top: -1px;
				}
			}
		}

		.n-menu--horizontal {
			.n-menu-item-content {
				.n-menu-item-content-header {
					overflow: initial;
				}
			}
		}
	}
}
</style>

<style lang="scss">
.main-nav {
	.n-dropdown-option-body,
	.n-dropdown-option-body--group,
	.n-dropdown-option-body__label {
		.item-badge {
			display: flex;
			justify-content: space-between;
			align-items: center;
			gap: 12px;

			:nth-child(1) {
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			:nth-child(2) {
				color: var(--fg-color);
				background: var(--hover-005-color);
				font-weight: bold;
				font-family: var(--font-family-mono);
				font-size: 10px;
				margin-right: 0px;
				height: 20px;
				line-height: 20px;
				border-radius: 8px;
				padding: 0 6px;
			}
		}

		&.n-dropdown-option-body--selected,
		&.n-dropdown-option-body--child-active {
			.item-badge {
				:nth-child(2) {
					color: var(--n-item-text-color-active);
					background: var(--primary-010-color);
				}
			}
		}
	}
}
</style>
