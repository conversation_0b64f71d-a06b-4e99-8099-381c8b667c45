<template>
	<div
		class="gallery-item overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
		:class="{ 'cursor-pointer': clickable }"
	>
		<div class="relative square-container">
			<!-- Naive UI Image component with preview -->
			<n-image
				:src="imageUrl"
				class="square-image"
				:preview-src="imageUrl"
				object-fit="cover"
				:show-toolbar-tooltip="true"
				:alt="item.name"
				loading="lazy"
				@click.stop="handleImageClick"
			/>

			<!-- Always visible name overlay at the bottom -->
			<div
				class="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-3 z-10 pointer-events-none"
			>
				<h3 class="text-white font-medium text-base truncate">{{ item.name }}</h3>
			</div>

			<!-- Full overlay with additional info on hover -->
			<div
				class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4 z-10 pointer-events-none"
				@click.stop
			>
				<h3 class="text-white font-medium text-lg mb-1">{{ item.name }}</h3>
				<p v-if="item.status_relation" class="text-white/80 text-sm">{{ item.status_relation }}</p>
				<p v-if="formattedDate" class="text-white/80 text-sm mt-1">{{ formattedDate }}</p>
			</div>
		</div>
	</div>
</template>

<script setup>
import { computed } from "vue"
import { NImage } from "naive-ui"

const config = useRuntimeConfig()

const props = defineProps({
	item: {
		type: Object,
		required: true
	},
	clickable: {
		type: Boolean,
		default: true
	}
})

const emit = defineEmits(["click"])

// Handle image click to emit the click event
const handleImageClick = () => {
	if (clickable) {
		emit("click", props.item)
	}
}

// Compute the image URL from Directus
const imageUrl = computed(() => {
	if (!props.item?.selfie?.id) return ""
	return `${config.public.directusUrl}/assets/${props.item.selfie.id}/${props.item.selfie.filename_download}`
})

// Format the attendance date
const formattedDate = computed(() => {
	if (!props.item.attendance_time) return ""

	try {
		const date = new Date(props.item.attendance_time)
		return date.toLocaleDateString("id-ID", {
			day: "numeric",
			month: "short",
			year: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		})
	} catch (e) {
		return ""
	}
})
</script>

<style scoped>
.gallery-item {
	display: flex;
	flex-direction: column;
	height: 100%;
}

/* Ensure the image container maintains a square aspect ratio */
.square-container {
	position: relative;
	width: 100%;
	height: 0;
	padding-bottom: 100%; /* Creates a square aspect ratio */
	overflow: hidden;
}

/* Style for the NImage component */
.square-image {
	position: absolute !important;
	top: 0;
	left: 0;
	width: 100% !important;
	height: 100% !important;
}

/* Override Naive UI image styles to maintain square ratio */
:deep(.n-image) {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}

:deep(.n-image img) {
	object-fit: cover;
	width: 100%;
	height: 100%;
}

/* Make sure overlays are above the image but don't interfere with clicks */
.gallery-item .absolute {
	pointer-events: none;
}
</style>
