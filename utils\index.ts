import Icon from "@/components/common/Icon.vue"
import { type Component, h } from "vue"

export type OS = "Unknown" | "Windows" | "MacOS" | "UNIX" | "Linux"

// Transform File Instance in base64 string
export function file2Base64(blob: Blob): Promise<string> {
	return new Promise((resolve, reject) => {
		const reader = new FileReader()
		reader.readAsDataURL(blob)
		reader.onload = () => resolve(reader.result as string)
		reader.onerror = error => reject(error)
	})
}
export function isEnvDev() {
	return process.env.NODE_ENV === "development"
}
export function isEnvTest() {
	return process.env.NODE_ENV === "test"
}
export function isEnvProd() {
	return process.env.NODE_ENV === "production"
}
export const isMobile = () => {
	const { isMobile } = useNuxtApp().$device as Device
	return isMobile
}

export function renderIcon(icon: Component | string) {
	if (typeof icon === "string") {
		return () => h(Icon, { name: icon })
	} else {
		return () => h(Icon, null, { default: () => h(icon) })
	}
}

export function getOS(): OS {
	let os: OS = "Unknown"
	if (navigator.userAgent.indexOf("Win") != -1) os = "Windows"
	if (navigator.userAgent.indexOf("Mac") != -1) os = "MacOS"
	if (navigator.userAgent.indexOf("X11") != -1) os = "UNIX"
	if (navigator.userAgent.indexOf("Linux") != -1) os = "Linux"

	return os
}

export const delay = (t: number) => {
	return new Promise(res => setTimeout(res, t))
}

export const formatISODate = (isoString: string) => {
	const date = new Date(isoString)

	const year = date.getUTCFullYear()
	const month = String(date.getUTCMonth() + 1).padStart(2, "0") // Months are zero-indexed
	const day = String(date.getUTCDate()).padStart(2, "0")
	const hours = String(date.getUTCHours()).padStart(2, "0")
	const minutes = String(date.getUTCMinutes()).padStart(2, "0")
	const seconds = String(date.getUTCSeconds()).padStart(2, "0")

	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

export const toISOString = (dateString: string) => {
	const [datePart, timePart] = dateString.split(" ")
	const [year, month, day] = datePart.split("-").map(Number)
	const [hours, minutes, seconds] = timePart.split(":").map(Number)

	const date = new Date(Date.UTC(year, month - 1, day, hours, minutes, seconds))
	return date.toISOString()
}

export const giftsToString = (gifts: string[]) => {
	let result = ""
	gifts.forEach(gift => {
		let text = ""
		switch (gift) {
			case "1": {
				text = "Amplop"
				break
			}
			case "2": {
				text = "Kado"
				break
			}
			case "3": {
				text = "Transfer"
				break
			}
		}
		if (result == "") {
			result += text
		} else {
			result += `, ${text}`
		}
	})
	return result
}

export const translateLevel = (level: string | number) => {
	switch (level) {
		case 1:
			return "Reguler"
		case 2:
			return "VIP"
		case 3:
			return "VVIP"
	}
}

export const optionsLevel = [
	{
		label: "Reguler",
		value: "1"
	},
	{
		label: "VIP",
		value: "2"
	},
	{
		label: "VVIP",
		value: "3"
	}
]

export const optionsRowPerPage = [
	{ label: "10/halaman", value: 10 },
	{ label: "25/halaman", value: 25 },
	{ label: "50/halaman", value: 50 },
	{ label: "100/halaman", value: 100 }
]

export const defaultTemplateGeneral = [
	{
		align: "center",
		type: "text",
		content: "================"
	},
	{
		align: "center",
		type: "size",
		content: "[event]"
	},
	{
		align: "center",
		type: "text",
		content: "================"
	},
	{
		align: "center",
		type: "text",
		content: ""
	},
	{
		align: "center",
		type: "qrcode",
		content: "[event]"
	},
	{
		align: "center",
		type: "text",
		content: "gunakan ini sebagai voucher souvenir"
	},
	{
		align: "center",
		type: "text",
		content: "------------------------"
	}
]

export const defaultTemplateGuest = [
	{
		align: "center",
		type: "text",
		content: "================"
	},
	{
		align: "center",
		type: "size",
		content: "[event]"
	},
	{
		align: "center",
		type: "text",
		content: "================"
	},
	{
		align: "center",
		type: "text",
		content: ""
	},
	{
		align: "left",
		type: "text",
		content: "nama:"
	},
	{
		align: "right",
		type: "text",
		content: "[nama-tamu]"
	},
	{
		align: "left",
		type: "text",
		content: "relasi:"
	},
	{
		align: "right",
		type: "text",
		content: "[status]"
	},
	{
		align: "left",
		type: "text",
		content: "level:"
	},
	{
		align: "right",
		type: "text",
		content: "[level]"
	},
	{
		align: "left",
		type: "text",
		content: "meja:"
	},
	{
		align: "right",
		type: "text",
		content: "[meja]"
	},
	{
		align: "left",
		type: "text",
		content: "pax:"
	},
	{
		align: "right",
		type: "text",
		content: "[pax]"
	},
	{
		align: "center",
		type: "text",
		content: ""
	},
	{
		align: "center",
		type: "qrcode",
		content: "[kode-tamu]"
	}
]

export const defaultTemplateEntrust = [
	{
		align: "center",
		type: "text",
		content: "================"
	},
	{
		align: "center",
		type: "size",
		content: "[event]"
	},
	{
		align: "center",
		type: "text",
		content: "================"
	},
	{
		align: "center",
		type: "text",
		content: ""
	},
	{
		align: "left",
		type: "text",
		content: "dititipkan oleh:"
	},
	{
		align: "right",
		type: "text",
		content: "[nama-tamu]"
	}
]
