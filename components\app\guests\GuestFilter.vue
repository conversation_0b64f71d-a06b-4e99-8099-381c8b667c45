<template>
	<n-card>
		<n-form>
			<n-form-item v-if="!viewport.isLessThan('desktop')" label="<PERSON><PERSON><PERSON><PERSON> Ko<PERSON>">
				<n-select
					v-model:value="selectedColumns"
					:options="columnOptions"
					placeholder="<PERSON><PERSON>h Kolom untuk Ditampilkan"
					multiple
					:render-tag="renderTag"
				/>
			</n-form-item>

			<n-space align="end">
				<!-- <n-form-item label="Filter Nama">
					<n-input
						v-model:value="filterNameModel"
						type="text"
						clearable
						placeholder="Filter Nama"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<n-form-item label="Filter Status">
					<n-input
						v-model:value="filterStatusModel"
						type="text"
						clearable
						placeholder="Filter Status"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item> -->
				<!-- <n-form-item label="Filter Kode">
					<n-input
						v-model:value="filterCodeModel"
						type="text"
						clearable
						placeholder="Filter Kode"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item> -->
				<n-form-item label="Filter Level">
					<n-select
						v-model:value="filterLevelModel"
						:options="optionsLevelExtend"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<!-- <n-form-item label="Filter Sesi">
					<n-input
						v-model:value="filterShiftModel"
						type="text"
						clearable
						placeholder="Filter sesi"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item> -->
				<!-- <n-form-item label="Filter Meja">
					<n-input
						v-model:value="filterTableModel"
						type="text"
						clearable
						placeholder="Filter meja"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item> -->
				<n-form-item>
					<template #label>
						Filter Kategori
						<n-popover placement="bottom-end" trigger="hover">
							<template #trigger>
								<Icon class="" color="#00B27B" :size="20" name="ion:information-circle-outline" />
							</template>
							Filter ini digunakan untuk
							<br />
							menampilkan tamu undangan
							<br />
							berdasarkan kategori
							<br />
							yang sudah diinput
							<br />
							(Ex: Tamu Ayah, Tamu UGM, dll)
						</n-popover>
					</template>
					<n-select
						v-model:value="filterCategoryModel"
						placeholder="Filter kategori"
						:options="categoryOption"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<!-- <n-form-item label="Filter Alamat">
					<n-input
						v-model:value="filterAddressModel"
						type="text"
						clearable
						placeholder="Filter Alamat"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<n-form-item label="Filter No. HP">
					<n-input
						v-model:value="filterPhoneModel"
						type="text"
						clearable
						placeholder="Filter no. HP"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<n-form-item label="Filter Pax">
					<n-input-number
						v-model:value="filterPaxModel"
						type="number"
						clearable
						placeholder="Filter pax"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<n-form-item label="Filter Souvenir">
					<n-input-number
						v-model:value="filterSouvenirModel"
						type="number"
						clearable
						placeholder="Filter souvenir"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<n-form-item label="Filter Catatan">
					<n-input
						v-model:value="filterNoteModel"
						type="text"
						clearable
						placeholder="Filter catatan"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item> -->
				<n-form-item label="Filter RSVP">
					<n-select
						v-model:value="filterRSVPModel"
						placeholder="Filter RSVP"
						:options="rsvpOptions"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<n-form-item label="Filter Mempelai">
					<n-select
						v-model:value="filterBrideModel"
						placeholder="Filter Mempelai"
						:options="brideOptions"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
				<n-form-item label="Filter Jenis Undangan">
					<n-select
						v-model:value="filterTypeInvitationModel"
						placeholder="Filter Jenis Undangan"
						:options="typeInvitationOptions"
						:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
					/>
				</n-form-item>
			</n-space>
			<n-form-item>
				<n-space>
					<n-button @click="resetFilter">Reset</n-button>
					<n-button type="primary" @click="apply">Terapkan</n-button>
				</n-space>
			</n-form-item>
		</n-form>
	</n-card>
</template>

<script setup>
import { NTag, NForm, NFormItem, NCard, NButton, NSpace, NSelect, NPopover } from "naive-ui"
import { onMounted } from "vue"

const viewport = useViewport()
const props = defineProps({
	agenda: Object
})
const emit = defineEmits(["change"])

const aggregateGuest = ref(null)
const { token } = useDirectusToken()

const route = useRoute()

const getAggregate = async () => {
	try {
		const items = await $fetch("https://directus.bagimomen.my.id/items/guest", {
			headers: {
				Authorization: `Bearer ${token.value}` // Include JWT token in Authorization header
			},
			params: {
				aggregate: {
					count: "id" // Example aggregate field
				},
				groupBy: ["category"],
				filter: {
					event: {
						_eq: route.params.id
					}
				}
			}
		})
		aggregateGuest.value = items
	} catch (error) {
		console.log(error)
	}
}

const categoryOption = computed(() => {
	const options = [{ value: "", label: "Semua" }]
	aggregateGuest.value?.data?.forEach(item => {
		if (item.category) options.push({ value: item.category, label: item.category })
	})
	return options
})

const rsvpOptions = [
	{ value: "", label: "Semua" },
	{ value: "1", label: "Bersedia hadir" },
	{ value: "0", label: "Berhalangan hadir" },
	{ value: "not yet", label: "Belum konfirmasi" }
]

onMounted(async () => {
	await getAggregate()
	await getTypeInvitationOptions()
})

let selectedColumns = defineModel()
// const selectedColumns = ref(["no", "name", "status_relation", "level", "code_guest", "actions"])

/* ------------------------------ Reactive Filter Models ------------------------------ */
const filterNameModel = ref("")
const filterCodeModel = ref("")
const filterStatusModel = ref("")
const filterLevelModel = ref("")
const filterShiftModel = ref("")
const filterTableModel = ref("")
const filterCategoryModel = ref("")
const filterAddressModel = ref("")
const filterPhoneModel = ref("")
const filterPaxModel = ref(null)
const filterSouvenirModel = ref(null)
const filterNoteModel = ref("")
const filterRSVPModel = ref("")
const filterBrideModel = ref("")
const filterTypeInvitationModel = ref("")

const brideOptions = [
	{ label: "Semua", value: "" },
	{ label: "CPP", value: "1" },
	{ label: "CPW", value: "2" }
]

const typeInvitationOptions = ref([])

const getTypeInvitationOptions = async () => {
	try {
		const response = await $fetch("https://directus.bagimomen.my.id/items/guest", {
			headers: {
				Authorization: `Bearer ${token.value}`
			},
			params: {
				filter: {
					event: {
						_eq: route.params.id
					}
				},
				aggregate: {
					count: "id"
				},
				groupBy: ["type_invitation"]
			}
		})

		const options = [{ label: "Semua", value: "" }]
		response.data.forEach(item => {
			if (item.type_invitation) {
				options.push({
					label: item.type_invitation,
					value: item.type_invitation
				})
			}
		})
		typeInvitationOptions.value = options
	} catch (error) {
		console.error(error)
	}
}

const resetFilter = () => {
	filterNameModel.value = ""
	filterCodeModel.value = ""
	filterStatusModel.value = ""
	filterLevelModel.value = ""
	filterShiftModel.value = ""
	filterTableModel.value = ""
	filterCategoryModel.value = ""
	filterAddressModel.value = ""
	filterPhoneModel.value = ""
	filterPaxModel.value = null
	filterSouvenirModel.value = null
	filterNoteModel.value = ""
	filterRSVPModel.value = ""
	filterBrideModel.value = ""
	filterTypeInvitationModel.value = ""
	apply()
}

const optionsLevelExtend = [
	{
		label: "Semua",
		value: ""
	},
	...optionsLevel
]

// Define available column options for selection
const columnOptions = [
	{ label: "Nama", value: "name", disabled: true },
	{ label: "Kode", value: "code_guest", disabled: true },
	{ label: "Status", value: "status_relation", disabled: true },
	{ label: "Level", value: "level", disabled: true },
	{ label: "Sesi", value: "shift" },
	{ label: "Meja", value: "table" },
	{ label: "Kategori", value: "category" },
	{ label: "Alamat", value: "address" },
	{ label: "No HP", value: "phone" },
	{ label: "Pax", value: "amount_guest" },
	{ label: "Souvenir", value: "amount_souvenir" },
	{ label: "Catatan", value: "note" },
	{ label: "RSVP", value: "rsvp", disabled: true },
	{ label: "Mempelai", value: "bride" },
	{ label: "Jenis Undangan", value: "type_invitation" },
	{ label: "Aksi", value: "actions", disabled: true }
]

const renderTag = ({ option, handleClose }) => {
	return h(
		NTag,
		{
			round: true,
			closable: true,
			onMousedown: e => {
				e.preventDefault()
			},
			onClose: e => {
				e.stopPropagation()
				handleClose()
			}
		},
		{ default: () => option.label }
	)
}
const apply = () => {
	// Create base filter
	const baseFilter = {
		_and: [
			{
				event: {
					_eq: props.agenda.id
				}
			}
		]
	}

	// Add conditions if input values are present
	if (filterNameModel.value) {
		baseFilter._and.push({
			name: {
				_contains: filterNameModel.value
			}
		})
	}
	if (filterCodeModel.value) {
		baseFilter._and.push({
			code_guest: {
				_contains: filterCodeModel.value
			}
		})
	}
	if (filterStatusModel.value) {
		baseFilter._and.push({
			status_relation: {
				_contains: filterStatusModel.value
			}
		})
	}
	if (filterLevelModel.value) {
		baseFilter._and.push({
			level: {
				_eq: filterLevelModel.value
			}
		})
	}
	if (filterShiftModel.value) {
		baseFilter._and.push({
			shift: {
				_contains: filterShiftModel.value
			}
		})
	}
	if (filterTableModel.value) {
		baseFilter._and.push({
			table: {
				_contains: filterTableModel.value
			}
		})
	}
	if (filterCategoryModel.value) {
		baseFilter._and.push({
			category: {
				_contains: filterCategoryModel.value
			}
		})
	}
	if (filterAddressModel.value) {
		baseFilter._and.push({
			address: {
				_contains: filterAddressModel.value
			}
		})
	}
	if (filterPhoneModel.value) {
		baseFilter._and.push({
			phone: {
				_contains: filterPhoneModel.value
			}
		})
	}
	if (filterPaxModel.value) {
		baseFilter._and.push({
			amount_guest: {
				_eq: filterPaxModel.value
			}
		})
	}
	if (filterSouvenirModel.value) {
		baseFilter._and.push({
			amount_souvenir: {
				_eq: filterSouvenirModel.value
			}
		})
	}
	if (filterNoteModel.value) {
		baseFilter._and.push({
			note: {
				_contains: filterNoteModel.value
			}
		})
	}

	if (filterRSVPModel.value) {
		if (filterRSVPModel.value === "not yet") {
			baseFilter._and.push({
				rsvp: {
					_eq: null
				}
			})
		} else {
			baseFilter._and.push({
				rsvp: {
					_eq: filterRSVPModel.value
				}
			})
		}
	}

	if (filterBrideModel.value) {
		baseFilter._and.push({
			bride: {
				_eq: filterBrideModel.value
			}
		})
	}

	if (filterTypeInvitationModel.value) {
		baseFilter._and.push({
			type_invitation: {
				_contains: filterTypeInvitationModel.value
			}
		})
	}

	// Update the fetchParam filter
	emit("change", baseFilter)
}
</script>

<style></style>
