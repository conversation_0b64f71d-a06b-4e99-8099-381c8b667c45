/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const useDialog: typeof import('naive-ui')['useDialog']
  const useLoadingBar: typeof import('naive-ui')['useLoadingBar']
  const useMessage: typeof import('naive-ui')['useMessage']
  const useNotification: typeof import('naive-ui')['useNotification']
}
