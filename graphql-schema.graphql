type OfType {
	kind: String
	name: String
	ofType: Type
}

type Type {
	kind: String
	name: String
	ofType: OfType
}

type Arg {
	name: String
	description: String
	defaultValue: String
	type: Type
}

type Directive {
	name: String
	description: String
	args: [Arg]
	locations: [String]
}

type Field {
	name: String
	description: String
	isDeprecated: Boolean
	deprecationReason: String
	type: Type
	args: [Arg]
}

type InputValue {
	name: String
	description: String
	type: Type
	defaultValue: String
}

type EnumValue {
	name: String
	description: String
	isDeprecated: Boolean
	deprecationReason: String
}

type Type {
	kind: String
	name: String
	description: String
	fields: [Field]
	inputFields: [InputValue]
	interfaces: [Type]
	enumValues: [EnumValue]
	possibleTypes: [Type]
}

type Schema {
	queryType: Type
	mutationType: Type
	subscriptionType: Type
	types: [Type]
	directives: [Directive]
}

type Data {
	__schema: Schema
}

type AutogeneratedMainType {
	data: Data
}
