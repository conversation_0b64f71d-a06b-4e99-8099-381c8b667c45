<template>
	<div>
		<n-space vertical size="large" class="mt-5">
			<n-space align="center" justify="space-between">
				<n-input
					v-model:value="fetchParam.search"
					placeholder="Cari Tamu"
					clearable
					@keyup.enter="loadTemplates(fetchParam)"
					@input="delayedSearch"
					@clear="handleClear"
				/>
				<n-space align="center">
					<n-button type="primary" @click="showAddForm = true">buat template</n-button>
				</n-space>
			</n-space>
			<n-flex v-if="templatesData" align="center">
				<n-card
					v-for="template in templatesData"
					:key="template.id"
					v-wave
					content-style="padding: 12px;"
					:style="viewport.isLessThan('tablet') ? 'width: 100% !important' : 'width: 49% !important'"
				>
					<n-space justify="space-between" align="center">
						<n-space align="center" class="m-0">
							<n-p depth="1" class="m-0 p-0 leading-none">{{ template.title }}</n-p>
						</n-space>
						<n-space align="center">
							<n-button size="small" circle type="warning" @click="handleEdit(template)">
								<template #icon>
									<Icon :name="EditIcon" />
								</template>
							</n-button>
							<n-button size="small" circle type="error" @click="handleDelete(template)">
								<template #icon>
									<Icon :name="DeleteIcon" />
								</template>
							</n-button>
						</n-space>
					</n-space>
				</n-card>
			</n-flex>
		</n-space>
		<SenderTemplateAdd
			v-model:show="showAddForm"
			:agenda="props.agenda"
			@after-submit="handleAfterSubmit"
			@close="showAddForm = false"
		/>

		<SenderTemplateEdit
			v-model:show="showEditForm"
			:agenda="props.agenda"
			:template="selectedTemplate"
			@after-submit="handleAfterSubmit"
			@close="showEditForm = false"
		/>
	</div>
</template>

<script setup>
import { NSpace, NP, NInput, useLoadingBar, useMessage, NButton, NCard, useDialog, NFlex } from "naive-ui"

const props = defineProps({
	agenda: Object
})

const EditIcon = "la:edit"
const DeleteIcon = "la:trash-alt"

const loadingBar = useLoadingBar()
const message = useMessage()
const viewport = useViewport()
const dialog = useDialog()
const { deleteItems } = useDirectusItems()

const showAddForm = ref(false)

const fetchParam = reactive({
	filter: {
		event: {
			_eq: props.agenda.id
		}
	},
	search: ""
})

const { data: templatesData, meta: templatesMeta, load: loadTemplates, loading: loadingTemplates } = useGetTemplates()

const delayedSearch = useDebounceFn(async () => {
	fetchParam.page = 1
	await loadTemplates(fetchParam)
}, 500)

const handleClear = async () => {
	fetchParam.search = ""
	fetchParam.page = 1
	await loadTemplates(fetchParam)
}

const deleteTemplate = async template => {
	try {
		await deleteItems({
			collection: "template",
			items: [template.id]
		})
		message.success(`template ${template.title} dihapus`)
	} catch (e) {
		console.error(e)
		message.error("Error deleting template")
	}
}

const handleDelete = template => {
	const d = dialog.warning({
		title: "Konfirmasi",
		content: "Yakin hapus template ini?",
		positiveText: "Hapus",
		negativeText: "Batalkan",
		onPositiveClick: async () => {
			d.loading = true
			await deleteTemplate(template)
			await loadTemplates(fetchParam)
			d.loading = false
		}
	})
}

const showEditForm = ref(false)
const selectedTemplate = ref(null)
const handleEdit = template => {
	showEditForm.value = true
	selectedTemplate.value = template
}

const handleAfterSubmit = async () => {
	showAddForm.value = false
	showEditForm.value = false
	await loadTemplates(fetchParam)
}

onMounted(async () => {
	await loadTemplates(fetchParam)
})
</script>

<style></style>
