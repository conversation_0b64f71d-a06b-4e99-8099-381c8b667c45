<template>
	<n-table>
		<thead>
			<tr>
				<th><PERSON>a</th>
				<th>Level</th>
				<th>Pax</th>
				<th><PERSON><PERSON><PERSON><PERSON></th>
			</tr>
		</thead>
		<tbody>
			<tr v-for="guest of guests" v-wave :key="guest.id" @click="emitSelect(guest)">
				<td>
					<div class="product flex items-center">
						<div class="product-info">
							<div class="product-name">
								{{ guest.name }}
							</div>
							<div class="product-category">
								{{ guest.status_relation || "-" }}
							</div>
						</div>
					</div>
				</td>
				<td>
					<div class="stock">
						<n-tag v-if="guest.level === 3" round type="warning">VVIP</n-tag>
						<n-tag v-if="guest.level === 2" round type="warning">VIP</n-tag>
						<n-tag v-if="guest.level === 1" round>Reguler</n-tag>
					</div>
				</td>
				<td>
					<div class="price">{{ guest.amount_guest }} Pax</div>
				</td>
				<td>
					<div class="stock">
						<n-tag v-if="guest.presence && guest.on_Site" round :bordered="false" type="success">
							On Site
							<template #icon>
								<Icon name="icon-park-solid:check-one" />
							</template>
						</n-tag>
						<n-tag v-if="guest.presence && !guest.on_Site" round :bordered="false" type="success">
							Hadir
							<template #icon>
								<Icon name="icon-park-solid:check-one" />
							</template>
						</n-tag>
						<n-tag v-if="!guest.presence" round :bordered="false" type="error">
							Belum Hadir
							<template #icon>
								<Icon name="icon-park-solid:handle-x" />
							</template>
						</n-tag>
					</div>
				</td>
			</tr>
		</tbody>
	</n-table>
</template>

<script setup>
import { NTable, NTag } from "naive-ui"
import Icon from "@/components/common/Icon.vue"

defineProps({
	guests: Array
})
const emits = defineEmits(["select"])
const emitSelect = guest => {
	emits("select", guest)
}
</script>

<style scoped lang="scss">
.product {
	.product-image {
		.n-image {
			:deep(img) {
				border-radius: var(--border-radius-small);
			}
		}
	}

	.product-info {
		.product-name {
			font-weight: 500;
			font-size: 16px;
			line-height: 1.2;
		}
		.product-category {
			opacity: 0.6;
		}
	}
}

.price {
	white-space: nowrap;
}

.orders {
	text-align: right;
	.orders-value {
		white-space: nowrap;
	}
}
</style>
