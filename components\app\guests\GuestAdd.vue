<template>
	<!-- @after-enter="handleAfterEnter" -->
	<CustomDrawer v-model:show="show" title="Tambah Tamu" @close="handleClose">
		<n-form ref="formGuestRef" :rules="rules" :model="guestModel">
			<n-form-item label="Nama" path="name">
				<n-input v-model:value="guestModel.name" type="text" clearable placeholder="Masukkan nama tamu" />
			</n-form-item>
			<n-form-item label="Status/Relasi" path="status_relation">
				<n-input
					v-model:value="guestModel.status_relation"
					type="text"
					clearable
					placeholder="Masukkan status/relasi tamu"
				/>
			</n-form-item>
			<n-grid :span="24" :x-gap="8">
				<n-form-item-gi :span="12" label="Level" path="level">
					<n-select v-model:value="guestModel.level" :options="optionsLevel" />
				</n-form-item-gi>
				<n-form-item-gi :span="12" label="Sesi" path="shift">
					<n-input v-model:value="guestModel.shift" type="text" clearable placeholder="Masukkan sesi tamu" />
				</n-form-item-gi>
			</n-grid>
			<n-grid :span="24" :x-gap="8">
				<n-form-item-gi :span="12" label="Meja" path="table">
					<n-input
						v-model:value="guestModel.table"
						type="text"
						clearable
						placeholder="Masukkan nama meja tamu"
					/>
				</n-form-item-gi>
				<n-form-item-gi :span="12" label="Kategori" path="category">
					<n-input
						v-model:value="guestModel.category"
						type="text"
						clearable
						placeholder="Masukkan kategori tamu"
					/>
				</n-form-item-gi>
			</n-grid>
			<n-form-item label="Alamat" path="address">
				<n-input v-model:value="guestModel.address" type="text" clearable placeholder="Masukkan alamat tamu" />
			</n-form-item>
			<n-form-item label="No. HP" path="phone">
				<n-input v-model:value="guestModel.phone" type="text" clearable placeholder="Mohon diawali dengan 62" />
			</n-form-item>
			<n-grid :span="24" :x-gap="8">
				<n-form-item-gi :span="12" label="Jumlah tamu" path="amount_guest">
					<n-input-number
						v-model:value="guestModel.amount_guest"
						clearable
						:min="0"
						style="width: 100%"
						placeholder="Masukkan jumlah tamu"
					/>
				</n-form-item-gi>
				<n-form-item-gi :span="12" label="Jumlah souvenir" path="amount_souvenir">
					<n-input-number
						v-model:value="guestModel.amount_souvenir"
						clearable
						:min="0"
						style="width: 100%"
						placeholder="Masukkan jumlah souvenir"
					/>
				</n-form-item-gi>
			</n-grid>
			<n-form-item label="Catatan" path="note">
				<n-input
					v-model:value="guestModel.note"
					type="text"
					clearable
					placeholder="Masukkan catatan mengenai tamu"
				/>
			</n-form-item>
			<n-form-item label="Jenis Undangan" path="type_invitation">
				<n-select v-model:value="guestModel.type_invitation" :options="optionsTypeInvitation" />
			</n-form-item>
			<n-form-item label="RSVP" path="RSVP">
				<n-select v-model:value="guestModel.rsvp" :options="optionRSVP" />
			</n-form-item>
			<n-form-item v-show="false" label="Kode (optional)" path="code_guest">
				<n-input
					v-model:value="guestModel.code_guest"
					type="text"
					clearable
					placeholder="Masukkan kode tamu (tanpa spasi atau karakter khusus)"
					@input="validateCodeGuest"
				/>
			</n-form-item>
		</n-form>
		<template #footer>
			<n-button block :loading="submitLoading" type="primary" @click="handleSubmit">Simpan</n-button>
		</template>
	</CustomDrawer>
</template>

<script setup>
import {
	NGrid,
	NFormItemGi,
	NSelect,
	NForm,
	NFormItem,
	NInputNumber,
	NInput,
	NButton,
	useLoadingBar,
	useMessage
} from "naive-ui"
import CustomDrawer from "@/components/app/CustomDrawer.vue"
import ShortUniqueId from "short-unique-id"

const props = defineProps({
	agenda: Object
})

const emit = defineEmits(["close", "after-submit"])

const show = defineModel()

const loadingBar = useLoadingBar()
const message = useMessage()
const { createItems } = useDirectusItems()
const formGuestRef = ref(null)

const guestModel = ref({
	code_guest: null,
	name: null,
	status_relation: null,
	level: "1",
	shift: null,
	table: null,
	category: null,
	address: null,
	phone: null,
	amount_guest: 2,
	amount_souvenir: 1,
	note: null,
	type_invitation: null,
	rsvp: null
})

const resetGuestModel = () => {
	guestModel.value = {
		code_guest: null,
		name: null,
		status_relation: null,
		level: "1",
		shift: null,
		table: null,
		category: null,
		address: null,
		phone: null,
		amount_guest: 2,
		amount_souvenir: 1,
		note: null,
		type_invitation: null,
		rsvp: null
	}
}

const optionRSVP = [
	{ label: "Belum Mengisi", value: null },
	{ label: "Tidak bisa berhadir", value: "0" },
	{ label: "Akan Berhadir", value: "1" }
]

const optionsTypeInvitation = [
	{ label: "Digital", value: "digital" },
	{ label: "Cetak", value: "cetak" },
	{ label: "Cetak & Digital", value: "cetak & digital" }
]

const rules = {
	name: {
		required: true,
		message: "Nama tidak boleh kosong",
		trigger: ["input", "blur"]
	},
	code_guest: {
		validator: (_, value) => {
			if (!value) return true // Optional field
			return /^[a-zA-Z0-9_-]*$/.test(value) || "Kode tidak boleh mengandung spasi atau karakter khusus"
		},
		trigger: ["input", "blur"]
	}
}

const submitLoading = ref(false)

const handleSubmit = e => {
	e.preventDefault()
	formGuestRef.value?.validate(async errors => {
		if (!errors) {
			await submitData()
		} else {
			console.error(errors)
		}
	})
}

const { randomUUID } = new ShortUniqueId({ length: 4 })

const submitData = async () => {
	submitLoading.value = true
	loadingBar.start()
	try {
		const prefix = `${props.agenda?.code_event}-`
		if (guestModel.value.code_guest) {
			if (!guestModel.value.code_guest.startsWith(prefix)) {
				guestModel.value.code_guest = prefix + guestModel.value.code_guest
			}
		} else {
			guestModel.value.code_guest = prefix + randomUUID()
		}

		guestModel.value.event = props.agenda.id
		const body = {
			...props.selectedGuest,
			...guestModel.value
		}
		await createItems({ collection: "guest", items: body })
		message.success("data telah ditambahkan")
		emit("after-submit")
		resetGuestModel()
		loadingBar.finish()
	} catch (e) {
		console.error(e)
		message.error("bad request, kode tamu sama")
		loadingBar.error()
	} finally {
		submitLoading.value = false
	}
}

const validateCodeGuest = () => {
	// Remove spaces and special characters in real-time
	if (guestModel.value.code_guest) {
		guestModel.value.code_guest = guestModel.value.code_guest.replace(/[^a-zA-Z0-9_-]/g, "")
	}
}

const handleClose = () => {
	emit("close")
}
</script>

<style lang="scss" scoped>
::v-deep(.n-input__suffix) {
	gap: 8px;
}
</style>
