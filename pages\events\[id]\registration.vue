<template>
	<div class="page">
		<n-result
			v-if="!haveAccess"
			status="warning"
			title="Aks<PERSON> ditolak"
			description="anda tidak memiliki akses halaman ini"
		>
			<template #footer>
				<n-button @click="refresh">Refresh</n-button>
			</template>
		</n-result>
		<template v-else-if="agendaData">
			<n-h3 prefix="bar">
				<n-flex justify="space-between">
					<h3>REGISTRASI</h3>
					<span>
						<n-popselect
							v-if="agendaData.greeting_screen"
							v-model:value="selectedPubChannel"
							:options="channelOption"
						>
							<n-button circle>
								<template #icon>
									<Icon name="ph:broadcast" />
								</template>
							</n-button>
						</n-popselect>
						<n-button v-if="agendaData.print_feature && !isIos" class="ml-2" circle @click="connectPrint">
							<template #icon>
								<Icon name="clarity:connect-line" />
							</template>
						</n-button>
					</span>
				</n-flex>
			</n-h3>

			<n-tabs type="segment" default-value="Penerimaan" animated>
				<n-tab-pane name="Penerimaan" tab="Penerimaan">
					<RegistGuests :agenda="agendaData" @print-content="printContent" @print-entrust="printEntrust" />
				</n-tab-pane>
				<n-tab-pane name="Riwayat" tab="Riwayat">
					<RegistHistory :agenda="agendaData" @print-content="printContent" @print-entrust="printEntrust" />
				</n-tab-pane>
				<n-tab-pane name="Hadiah" tab="Hadiah">
					<RegistGift :agenda="agendaData" @print-content="printContent" @print-entrust="printEntrust" />
				</n-tab-pane>
			</n-tabs>
		</template>

		<n-float-button
			:left="`calc(50vw - ${floatButtonSize / 2}px)`"
			:bottom="30"
			:height="floatButtonSize"
			:width="floatButtonSize"
			type="primary"
			@click="showScanner = true"
		>
			<Icon name="carbon:scan-alt" :size="28" />
		</n-float-button>

		<!-- qr scanner -->
		<!-- regist form -->
		<!-- :style="{backgroundImage: `url(${urlBackground})`}" -->

		<CustomDrawer v-model:show="showScanner" title="Scanner" @close="showScanner = false">
			<n-alert v-if="noFrontCamera" title="Error" type="error">
				You don't seem to have a front camera on your device
			</n-alert>
			<n-alert v-if="noRearCamera" title="Error" type="error">
				You don't seem to have a rear camera on your device
			</n-alert>
			<n-alert v-if="scannerError" title="Error" type="error">
				{{ scannerError }}
			</n-alert>

			<!-- <n-alert v-if="resultScanner" title="Sukses" type="success">
        {{ resultScanner }}
      </n-alert> -->
			<n-spin :show="loadingScanner">
				<qrcode-stream
					:constraints="{ facingMode }"
					:paused="pausedScanner"
					:track="paintBoundingBox"
					:style="`height: ${scannerHeight}`"
					@camera-on="onCameraOn"
					@error="onScannerError"
					@detect="onDetect"
				>
					<n-space vertical>
						<n-button class="m-2" type="primary" strong secondary @click="switchCamera">
							<Icon name="fluent:camera-switch-20-filled" :size="24" />
						</n-button>
					</n-space>

					<!-- scanning animation -->
					<div
						v-if="!loadingScanner"
						class="ocrloader absolute left-1/2 top-0 transform -translate-x-1/2 z-10 mx-auto"
						style="pointer-events: none"
					>
						<p>Scanning</p>
						<em></em>
						<span></span>
					</div>
				</qrcode-stream>
				<qrcode-capture @detect="onDetect" />
			</n-spin>
		</CustomDrawer>

		<RegistDrawerConfirmation
			v-model:show="showForm"
			:selectedGuest="selectedGuest"
			:agenda="agendaData"
			@close="showForm = !showForm"
			@after-submit="handleAfterSubmit"
			@print-content="printContent"
			@print-entrust="printEntrust"
		/>
	</div>
</template>

<script setup>
import {
	NResult,
	NButton,
	NH3,
	NFloatButton,
	NTabs,
	NTabPane,
	NAlert,
	NSpace,
	NSpin,
	useLoadingBar,
	useMessage,
	useNotification,
	NPopselect,
	NFlex
} from "naive-ui"
import { QrcodeStream, QrcodeCapture } from "vue-qrcode-reader"
import { useMainStore } from "@/stores/main"
import _includes from "lodash/includes"
import WebBluetoothReceiptPrinter from "~/webbluetooth-receipt-printer.esm"
import ReceiptPrinterEncoder from "@point-of-sale/receipt-printer-encoder"
import ReceiptPrinterStatus from "~/receipt-printer-status.esm.js"

definePageMeta({
	name: "Registration",
	title: "Registration"
})

const user = useDirectusUser()
const isAdmin = ref(user.value.role.name === "Administrator")
const { getItemById, getItems } = useDirectusItems()
const mainStore = useMainStore()
const loadingBar = useLoadingBar()
const route = useRoute()
const message = useMessage()
const notification = useNotification()
const selectedPubChannel = useSelectedPubChannel()
const { isIos } = useDevice()

const haveAccess = ref(true)
const listAccess = ref([])
const agendaData = ref(null)

// const viewport = useViewport();
// scanner
const floatButtonSize = ref(50)
const showScanner = ref(false)
const loadingScanner = ref(true)
const resultScanner = ref(null)
const pausedScanner = ref(false)
const noFrontCamera = ref(false)
const noRearCamera = ref(false)
const scannerError = ref(null)
const facingMode = ref("environment")
const scannerHeight = "90vh"

// printer
const receiptPrinter = isIos ? null : new WebBluetoothReceiptPrinter()
const lastPrinter = useLastPrinter()
let encoder = new ReceiptPrinterEncoder({ columns: 32 })
let printerStatus = null

if (!isIos) {
	receiptPrinter.addEventListener("connected", device => {
		console.log(`Connected to ${device.name} (#${device.id})`)

		encoder = new ReceiptPrinterEncoder({
			language: device.language,
			columns: 32
		})
		/* Store device for reconnecting */
		lastPrinter.value = device

		printerStatus = new ReceiptPrinterStatus({
			printer: receiptPrinter,
			language: device.language
		})

		printerStatus.addEventListener("connected", () => {
			message.success(`status: Connected to ${device.name}`)
		})

		printerStatus.addEventListener("unsupported", () => {
			message.error(`status: Unsupported printer ${device.name}`)
		})

		printerStatus.addEventListener("disconnected", () => {
			message.info(`status: Disconnected from ${device.name}`)
		})
	})

	receiptPrinter.addEventListener("disconnected", () => {
		console.log(`printer disconnected`)
	})
}

const connectPrint = async () => {
	await receiptPrinter.connect()
}

const reconnectPrint = async () => {
	if (lastPrinter.value) {
		await receiptPrinter.reconnect(lastPrinter.value)
		await new Promise(resolve => setTimeout(resolve, 500))
	}
}

const replaceKeyword = (text, guest) => {
	const resultText = text
	return resultText
		.replaceAll("[event]", agendaData.value.title || "")
		.replaceAll("[tanggal]", agendaData.value.start || "")
		.replaceAll("[lokasi]", agendaData.value.location || "")
		.replaceAll("[kode-tamu]", guest.code_guest || "")
		.replaceAll("[nama-tamu]", guest.name || "")
		.replaceAll("[status]", guest.status_relation || "")
		.replaceAll("[level]", guest.level == 1 ? "reguler" : guest.level == 2 ? "VIP" : "VVIP")
		.replaceAll("[meja]", guest.table || "")
		.replaceAll("[pax]", guest.amount_guest || "")
}

const transformInputToEncoder = (inputArray, guest) => {
	inputArray.forEach(command => {
		switch (command.align) {
			case "left":
				encoder.align("left")
				break
			case "center":
				encoder.align("center")
				break
			case "right":
				encoder.align("right")
				break
			default:
				console.warn(`Unknown command type: ${command.align}`)
		}

		switch (command.type) {
			case "size":
				encoder.size(2).line(replaceKeyword(command.content, guest)).size(1)
				break
			case "text":
				encoder.line(replaceKeyword(command.content, guest))
				break
			case "bold":
				encoder.bold(true).line(replaceKeyword(command.content, guest)).bold(false)
				break
			case "italic":
				encoder.italic(true).line(replaceKeyword(command.content, guest)).italic(false)
				break
			case "qrcode":
				encoder.qrcode(replaceKeyword(command.content, guest))
				break
			default:
				console.warn(`Unknown command type: ${command.align}`)
		}
	})

	encoder.newline(3)
	return encoder.encode() // Generate the final ESC/POS commands
}

const printContent = async guest => {
	try {
		if (!printerStatus || !printerStatus.connected) {
			await reconnectPrint()
		}

		if (printerStatus && printerStatus.connected) {
			const template =
				agendaData.value.print_template && agendaData.value.print_template.template_guest
					? JSON.parse(agendaData.value.print_template.template_guest)
					: defaultTemplateGuest

			const result = transformInputToEncoder(template, guest)

			receiptPrinter.print(result)
		} else {
			message.error("Printer belum terhubung")
		}
	} catch (error) {
		message.error("Gagal mencetak")
	}
}

const printEntrust = async guest => {
	// console.log("print entrust", guest)
	try {
		if (!printerStatus || !printerStatus.connected) {
			await reconnectPrint()
		}

		if (printerStatus && printerStatus.connected) {
			const template = defaultTemplateEntrust

			const result = transformInputToEncoder(template, guest)

			receiptPrinter.print(result)
		} else {
			message.error("Printer belum terhubung")
		}
	} catch (error) {
		message.error("Gagal mencetak")
	}
}

// data
const fetchGuestData = ref(null)
const fetchStatus = ref("idle")
const selectedGuest = ref(null)
const showForm = ref(false)

const channelOption = ref([
	{
		label: "global",
		value: "global"
	},
	{
		label: "usher",
		value: "usher"
	}
])

const fetchAgenda = async () => {
	try {
		const item = await getItemById({
			collection: "event",
			id: route.params.id,
			params: {
				fields: ["*", "users.*", "print_template.*"]
			}
		})
		item.users.forEach(user => {
			listAccess.value.push(user.directus_users_id)
		})
		agendaData.value = item
	} catch (e) {
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
		} else {
			console.error(e)
			message.error(e)
		}
	}
}

const refresh = e => {
	mainStore.softReload()
	return e
}

const onCameraOn = () => {
	loadingScanner.value = false
}

const onScannerError = err => {
	const triedFrontCamera = this.facingMode === "user"
	const triedRearCamera = this.facingMode === "environment"

	const cameraMissingError = err.name === "OverconstrainedError"

	if (triedRearCamera && cameraMissingError) {
		noRearCamera.value = true
	}

	if (triedFrontCamera && cameraMissingError) {
		noFrontCamera.vale = true
	}

	if (err.name === "NotAllowedError") {
		scannerError.value += "you need to grant camera access permission"
	} else if (err.name === "NotFoundError") {
		scannerError.value += "no camera on this device"
	} else if (err.name === "NotSupportedError") {
		scannerError.value += "secure context required (HTTPS, localhost)"
	} else if (err.name === "NotReadableError") {
		scannerError.value += "is the camera already in use?"
	} else if (err.name === "OverconstrainedError") {
		scannerError.value += "installed cameras are not suitable"
	} else if (err.name === "StreamApiNotSupportedError") {
		scannerError.value += "Stream API is not supported in this browser"
	} else if (err.name === "InsecureContextError") {
		scannerError.value +=
			"Camera access is only permitted in secure context. Use HTTPS or localhost rather than HTTP."
	} else {
		scannerError.value += err.message
	}

	console.error(err.message)
}

const fetchGuests = async idGuest => {
	fetchStatus.value = "pending"
	loadingBar.start()
	try {
		const items = await getItems({
			collection: "guest",
			params: {
				filter: {
					code_guest: {
						_eq: idGuest
					}
				}
			}
		})
		console.log("items", items)
		fetchGuestData.value = items
		fetchStatus.value = "success"
		loadingBar.finish()
	} catch (e) {
		loadingBar.error()
		fetchStatus.value = "error"
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
			fetchStatus.value = errorMessage
		} else {
			console.error(e)
			message.error(e)
			fetchStatus.value = e
		}
	}
}

const onDetect = async detectedCodes => {
	// resultScanner.value = JSON.stringify(detectedCodes.map((code) => code.rawValue))
	resultScanner.value = detectedCodes[0].rawValue
	pausedScanner.value = true
	// TODO: fetch guest
	await fetchGuests(resultScanner.value)
	console.log("after scan", fetchGuestData.value)
	if (fetchGuestData.value.length < 1) {
		notification.error({
			content: `Tamu tidak ditemukan`,
			description: `Error`,
			duration: 2000
		})
	} else {
		selectedGuest.value = fetchGuestData.value[0]
		showForm.value = true
	}
	await timeout(500)
	pausedScanner.value = false
}

const timeout = ms => {
	return new Promise(resolve => {
		window.setTimeout(resolve, ms)
	})
}

const handleAfterSubmit = () => {
	showForm.value = !showForm.value
}

const switchCamera = () => {
	switch (facingMode.value) {
		case "environment":
			facingMode.value = "user"
			break
		case "user":
			facingMode.value = "environment"
			break
	}
}

const paintBoundingBox = (detectedCodes, ctx) => {
	for (const detectedCode of detectedCodes) {
		const {
			boundingBox: { x, y, width, height }
		} = detectedCode

		ctx.lineWidth = 2
		ctx.strokeStyle = "#ff61c9"
		ctx.strokeRect(x, y, width, height)
	}
}

document.documentElement.style.setProperty("--scanner-height", scannerHeight)

onMounted(async () => {
	await fetchAgenda()
	if (!isAdmin.value) {
		haveAccess.value = _includes(listAccess.value, user.value.id) ? true : false
	}
	if (lastPrinter.value) {
		await reconnectPrint()
	}
})
</script>

<style lang="scss" scoped>
.ocrloader p::before {
	content: "";
	display: inline-block;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: var(--primary-color);
	position: relative;
	right: 4px;
}
.ocrloader p {
	color: var(--primary-color);
	position: absolute;
	bottom: 30px;
	left: 50%;
	transform: translateX(-50%);
	font-size: 16px;
	font-weight: 600;
	animation: blinker 1.5s linear infinite;
	font-family: sans-serif;
	text-transform: uppercase;
}
.ocrloader {
	width: 100%;
	height: 100%;
	position: relative;
}
.ocrloader span {
	position: absolute;
	left: 35px;
	top: 0;
	width: 85%;
	height: 5px;
	background-color: var(--primary-color);
	box-shadow:
		0 0 10px 1px var(--primary-color),
		0 0 1px 1px var(--primary-color);
	z-index: 1;
	transform: translateY(95px);
	animation: move 2s cubic-bezier(0.15, 0.54, 0.76, 0.74);
	animation-iteration-count: infinite;
}
.ocrloader:before,
.ocrloader:after,
.ocrloader em:after,
.ocrloader em:before {
	border-color: var(--primary-color);
	content: "";
	position: absolute;
	width: 45px;
	height: 46px;
	border-style: solid;
	border-width: 0px;
}
.ocrloader:before {
	left: 0;
	top: 0;
	border-left-width: 5px;
	border-top-width: 5px;
	border-radius: 5px 0 0 0;
}
.ocrloader:after {
	right: 0;
	top: 0;
	border-right-width: 5px;
	border-top-width: 5px;
	border-radius: 0 5px 0 0;
}
.ocrloader em:before {
	left: 0;
	bottom: 0;
	border-left-width: 5px;
	border-bottom-width: 5px;
	border-radius: 0 0 0 5px;
}
.ocrloader em:after {
	right: 0;
	bottom: 0;
	border-right-width: 5px;
	border-bottom-width: 5px;
	border-radius: 0 0 5px 0;
}
@keyframes move {
	0%,
	100% {
		transform: translateY(calc(var(--scanner-height) - 20vh));
	}
	50% {
		transform: translateY(0%);
	}
	75% {
		transform: translateY(calc(var(--scanner-height) - 5vh));
	}
}
@keyframes blinker {
	50% {
		opacity: 0;
	}
}
</style>
