import { renderIcon } from "@/utils"
import { h } from "vue"
import { RouterLink } from "vue-router"
import { type MenuMixedOption } from "naive-ui/es/menu/src/interface"

const HomeIcon = "carbon:home"
const EventIcon = "carbon:event"

/* eslint-disable @typescript-eslint/no-unused-vars */
export default function getItems(mode: "vertical" | "horizontal", collapsed: boolean): MenuMixedOption[] {
	return [
		{
			label: () =>
				h(
					RouterLink,
					{
						to: {
							name: "Home"
						}
					},
					{ default: () => "Home" }
				),
			key: "Home",
			icon: renderIcon(HomeIcon)
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: "/events"
					},
					{ default: () => "Events" }
				),
			key: "Events",
			icon: renderIcon(EventIcon)
		}
	]
}
