import { type Guest } from "~/types/globals"

// Define the GalleryItem type based on Guest with selfie
interface GalleryItem extends Guest {
	selfie?: {
		id: string
		filename_download: string
	}
}

const { getItems } = useDirectusItems()

export const useGetGallery = () => {
	const data = ref<GalleryItem[] | null>(null)
	const meta = ref(null)
	const loading = ref(false)
	const error = ref<Error | null>(null)

	const load = async (fetchParams: any) => {
		loading.value = true
		error.value = null

		try {
			// Ensure we're only fetching guests with selfie images
			const modifiedParams = {
				...fetchParams,
				filter: {
					...fetchParams.filter,
					selfie: {
						_nnull: true
					}
				},
				fields: [
					"id",
					"name",
					"status_relation",
					"code_guest",
					"selfie.id",
					"selfie.filename_download",
					"date_created",
					"date_updated",
					"attendance_time"
				],
				meta: "*"
			}

			const response = await getItems<GalleryItem>({
				collection: "guest",
				params: modifiedParams
			})

			// Type assertion to handle the response structure
			const items = response as unknown as { data: GalleryItem[]; meta: any }
			data.value = items.data
			meta.value = items.meta
		} catch (err) {
			console.error("Error fetching gallery:", err)
			error.value = err instanceof Error ? err : new Error(String(err))
		} finally {
			loading.value = false
		}
	}

	return { data, meta, load, loading, error }
}
