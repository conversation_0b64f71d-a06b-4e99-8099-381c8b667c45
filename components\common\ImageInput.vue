<template>
	<div class="image-input-container">
		<!-- Preview of selected/captured image -->
		<div v-if="modelValue" class="image-preview" :style="{ aspectRatio: previewAspectRatio }">
			<img :src="modelValue" class="preview-image" />
			<div class="image-actions">
				<n-button circle type="error" @click="clearImage">
					<template #icon>
						<Icon name="carbon:trash-can" />
					</template>
				</n-button>
			</div>
		</div>

		<!-- Input options when no image is selected -->
		<div v-else class="input-options">
			<!-- Camera option -->
			<CameraInput
				:title="cameraTitle"
				:button-text="cameraButtonText"
				:max-height="cameraMaxHeight"
				@capture="onCameraCapture"
			>
				<template #trigger="{ openCamera }">
					<n-button @click="openCamera" class="input-button">
						<template #icon>
							<Icon name="carbon:camera" />
						</template>
						{{ cameraButtonText }}
					</n-button>
				</template>
			</CameraInput>

			<!-- File upload option -->
			<n-upload accept="image/*" :show-file-list="false" @change="onFileChange" :max="1">
				<n-button class="input-button">
					<template #icon>
						<Icon name="carbon:folder" />
					</template>
					{{ uploadButtonText }}
				</n-button>
			</n-upload>
		</div>
	</div>
</template>

<script setup>
import { computed } from "vue"
import { NButton, NUpload } from "naive-ui"
import CameraInput from "./CameraInput.vue"

const props = defineProps({
	modelValue: {
		type: String,
		default: ""
	},
	cameraTitle: {
		type: String,
		default: "Take Photo"
	},
	cameraButtonText: {
		type: String,
		default: "Camera"
	},
	uploadButtonText: {
		type: String,
		default: "Upload"
	},
	cameraMaxHeight: {
		type: String,
		default: "90vh"
	},
	aspectRatio: {
		type: String,
		default: "9/16" // Portrait by default
	}
})

const emit = defineEmits(["update:modelValue"])

// Computed property for preview aspect ratio
const previewAspectRatio = computed(() => {
	return props.aspectRatio
})

// Handle camera capture
const onCameraCapture = imageData => {
	emit("update:modelValue", imageData)
}

// Handle file upload
const onFileChange = options => {
	const { file } = options

	if (!file) return

	const reader = new FileReader()
	reader.onload = e => {
		emit("update:modelValue", e.target.result)
	}
	reader.readAsDataURL(file)
}

// Clear selected image
const clearImage = () => {
	emit("update:modelValue", "")
}
</script>

<style scoped>
.image-input-container {
	width: 100%;
}

.image-preview {
	position: relative;
	width: 100%;
	border-radius: 8px;
	overflow: hidden;
	background-color: #f5f5f5;
}

.preview-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.image-actions {
	position: absolute;
	top: 8px;
	right: 8px;
	display: flex;
	gap: 8px;
}

.input-options {
	display: flex;
	gap: 16px;
	justify-content: center;
}

.input-button {
	min-width: 120px;
}
</style>
