export const useAggregateRSVP = () => {
	const { $directus } = useNuxtApp()
	const rsvpAggregated = ref(null)
	const loading = ref(false)

	const going = ref(0)
	const sumGoing = ref(0)
	const notGoing = ref(0)
	const sumNotGoing = ref(0)
	const unconfirm = ref(0)
	const sumUnconfirm = ref(0)

	const load = async (eventID: string | number) => {
		loading.value = true
		const { token } = useDirectusToken()
		await $directus.setToken(token.value)
		const result = await $directus.query(
			`
      query AggregateRSVP($id: GraphQLStringOrFloat) {
        rsvp: guest_aggregated(filter: { event: { id: { _eq: $id } } }, groupBy: "rsvp") {
            group
            count {
              id
            }
            sum {
              amount_guest
            }
        }    
    }`,
			{ id: eventID }
		)

		result.rsvp.forEach((item: any) => {
			if (item.group.rsvp === "0") {
				notGoing.value = item.count.id
				sumNotGoing.value = item.sum.amount_guest
			}

			if (item.group.rsvp === "1") {
				going.value = item.count.id
				sumGoing.value = item.sum.amount_guest
			}

			if (!item.group.rsvp) {
				unconfirm.value = item.count.id
				sumUnconfirm.value = item.sum.amount_guest
			}
		})

		rsvpAggregated.value = result
		loading.value = false
	}

	return { load, loading, rsvpAggregated, going, notGoing, unconfirm, sumGoing, sumNotGoing, sumUnconfirm }
}
