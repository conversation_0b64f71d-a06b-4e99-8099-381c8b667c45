<template>
	<div
		class="qr-card border-2 p-0 justify-center text-center flex flex-col relative"
		:style="{
			backgroundColor: cardInputModel.backgroundColor,
			backgroundSize: 'cover',
			backgroundPosition: 'center',
			backgroundRepeat: 'no-repeat',
			fontFamily: cardInputModel.textFont,
			backgroundImage: urlBG
				? `url(${urlBG})`
				: 'url(https://directus.bagimomen.my.id/assets/46df6859-8aec-4b21-ae04-7350dc4b00ac/bannerDetailBlog.webp)'
		}"
	>
		<div
			v-if="level > 1"
			v-resize-text="{
				ratio: 0.35,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			class="absolute w-[10%] top-[3%] right-[3%] bg-[#d3c861] rounded-full z-20 text-center"
			style="color: #000000"
		>
			{{ level > 1 ? (level == 2 ? "VIP" : "VVIP") : "" }}
		</div>
		<div class="text-center" style="margin-top: 5%; padding: 2%">
			<h4
				v-resize-text="{
					ratio: 1.4,
					minFontSize: 10,
					maxFontSize: 500,
					delay: 200
				}"
				:style="{
					color: cardInputModel.titleColor,
					fontFamily: cardInputModel.textFont
				}"
			>
				{{ cardInputModel.title }}
			</h4>
			<h2
				v-resize-text="{
					ratio: 1.1,
					minFontSize: 10,
					maxFontSize: 500,
					delay: 200
				}"
				:style="{
					marginBottom: '5%',
					fontFamily: cardInputModel.nameFont,
					color: cardInputModel.nameColor
				}"
			>
				{{ cardInputModel.name }}
			</h2>
		</div>
		<div
			ref="qrsize"
			class="w-[35%] rounded-lg text-center justify-center align-center px-2 pt-2 bg-white mx-auto border-2 border-black"
		>
			<n-qr-code
				class="p-0 m-0"
				:value="qr"
				type="svg"
				:size="qrWidth"
				:icon-src="iconSrc"
				icon-background-color="#ffffff"
				error-correction-level="H"
			/>
		</div>
		<n-p
			v-resize-text="{
				ratio: 3,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			:style="{
				marginTop: '5%',
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			<template v-if="cardInputModel.language == 0">
				Kepada Yth.
				<br />
				Bapak/Ibu/Saudara/i
			</template>
			<template v-if="cardInputModel.language == 1">
				Dear.
				<br />
				Mr/Mrs/Ms
			</template>
		</n-p>
		<div
			v-resize-text="{
				ratio: 1.7,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			class="font-bold"
			:style="{
				marginBottom: '5%',
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ name }}
		</div>
		<div
			v-resize-text="{
				ratio: 1.7,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			class="font-semibold"
			:style="{
				marginBottom: '1.5%',
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ cardInputModel.location }}
		</div>
		<div
			v-resize-text="{
				ratio: 2.5,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			:style="{
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ cardInputModel.date }}
		</div>
		<div
			v-resize-text="{
				ratio: 2.5,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			:style="{
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ cardInputModel.session == 0 ? cardInputModel.time : shift }}
		</div>
		<div class="mt-auto" style="margin-bottom: 20%; padding-left: 10%; padding-right: 10%">
			<n-p
				v-resize-text="{
					ratio: 3,
					minFontSize: 10,
					maxFontSize: 500,
					delay: 200
				}"
				class="text-center font-light"
				:style="{
					color: cardInputModel.textColor,
					fontFamily: cardInputModel.textFont
				}"
			>
				{{ cardInputModel.info }}
			</n-p>
		</div>
	</div>
</template>

<script setup>
import { NQrCode, NP } from "naive-ui"
import VueResizeText from "vue3-resize-text"

defineProps({
	cardInputModel: {
		type: Object,
		required: true
	},
	urlBG: {
		type: String,
		required: false
	},
	name: {
		type: String,
		required: true
	},
	qr: {
		type: String,
		required: true
	},
	level: {
		type: Number,
		default: 1
	},
	shift: {
		type: String,
		default: ""
	}
})

const vResizeText = VueResizeText.ResizeText

const qrsize = ref(null)
const { width: qrWidth } = useElementSize(qrsize)

const iconSrc = "https://directus.bagimomen.my.id/assets/74d78007-c9b9-4cff-b099-4e85df2605ee/<EMAIL>"
</script>

<style></style>
