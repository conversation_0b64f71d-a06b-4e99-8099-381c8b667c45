export default defineNuxtRouteMiddleware((to, from) => {
	// Redirect from root to /events
	if (to.path === "/") {
		return navigateTo("/events")
	}

	// Match paths like '/events/[slug]' but exclude paths that already contain more segments like '/dashboard', '/guests', etc.
	const eventPathRegex = /^\/events\/([^/]+)$/ // Matches /events/[slug] with no further segments
	if (eventPathRegex.test(to.path)) {
		const eventSlug = to.params.slug || to.path.split("/events/")[1] // Extract slug
		if (eventSlug) {
			return navigateTo(`/events/${eventSlug}/dashboard`)
		}
	}
})
