<template>
	<div class="page">
		<!-- Header -->
		<n-h1 prefix="bar" class="mb-6">
			<h1 class="apply-font"><PERSON><PERSON></h1>
		</n-h1>
		<div class="mb-6">
			<n-space>
				<n-input
					v-model:value="searchQuery"
					placeholder="Cari tamu"
					clearable
					@keyup.enter="handleSearch"
					@clear="handleClear"
				>
					<template #prefix>
						<Icon name="carbon:search" />
					</template>
				</n-input>

				<n-button circle @click="fetchGallery" :loading="loading">
					<template #icon>
						<Icon name="mdi:reload" />
					</template>
				</n-button>
			</n-space>
		</div>

		<!-- Gallery Grid -->
		<GalleryGrid
			:items="galleryData"
			:meta="galleryMeta"
			:loading="loading"
			:error="error"
			:show-upload-button="false"
			:initial-page="currentPage"
			:initial-page-size="pageSize"
			@item-click="handleItemClick"
			@reload="fetchGallery"
			@page-change="handlePageChange"
			@page-size-change="handlePageSizeChange"
		/>

		<!-- We're using Naive UI's built-in image preview functionality instead of a custom lightbox -->
	</div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue"
import { NButton, NInput, NH1, NSpace, useMessage, useLoadingBar } from "naive-ui"
import Icon from "~/components/common/Icon.vue"
import GalleryGrid from "~/components/app/gallery/GalleryGrid.vue"
import { useGetGallery } from "~/composables/useGetGallery"
import _debounce from "lodash/debounce"

definePageMeta({
	name: "Gallery",
	title: "Gallery"
})

// Route info
const route = useRoute()
const eventId = computed(() => route.params.id)

// UI utilities
const message = useMessage()
const loadingBar = useLoadingBar()

// Gallery data
const { data: galleryData, meta: galleryMeta, load: loadGallery, loading, error } = useGetGallery()

// Pagination and search state
const currentPage = ref(1)
const pageSize = ref(24)
const searchQuery = ref("")

// No UI state needed for lightbox as we're using Naive UI's built-in preview

// Fetch parameters
const fetchParams = computed(() => ({
	filter: {
		event: {
			_eq: eventId.value
		},
		...(searchQuery.value
			? {
					_or: [
						{ name: { _contains: searchQuery.value } },
						{ status_relation: { _contains: searchQuery.value } },
						{ code_guest: { _contains: searchQuery.value } }
					]
				}
			: {})
	},
	sort: ["-attendance_time"],
	page: currentPage.value,
	limit: pageSize.value
}))

// Fetch gallery data
const fetchGallery = async () => {
	loadingBar.start()
	try {
		await loadGallery(fetchParams.value)
		loadingBar.finish()
	} catch (err) {
		console.error("Error fetching gallery:", err)
		message.error("Failed to load gallery")
		loadingBar.error()
	}
}

// No need to handle item click as Naive UI's NImage handles preview automatically
const handleItemClick = () => {
	// This function is kept for compatibility with GalleryGrid component
}

// Handle page change
const handlePageChange = page => {
	currentPage.value = page
	fetchGallery()
}

// Handle page size change
const handlePageSizeChange = size => {
	pageSize.value = size
	currentPage.value = 1 // Reset to first page when changing page size
	fetchGallery()
}

// Handle search
const handleSearch = () => {
	currentPage.value = 1 // Reset to first page when searching
	fetchGallery()
}

// Debounced search
const delayedSearch = _debounce(() => {
	currentPage.value = 1
	fetchGallery()
}, 500)

// Watch for search query changes
watch(searchQuery, () => {
	delayedSearch()
})

// Handle clear search
const handleClear = () => {
	searchQuery.value = ""
	fetchGallery()
}

// Fetch gallery on mount
onMounted(() => {
	fetchGallery()
})
</script>

<style lang="scss" scoped>
.page {
	padding-bottom: 2rem;
}
</style>
