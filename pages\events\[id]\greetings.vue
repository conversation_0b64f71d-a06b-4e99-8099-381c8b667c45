events/home
<template>
	<div class="page">
		<n-result
			v-if="!haveAccess"
			status="warning"
			title="Aks<PERSON> ditolak"
			description="anda tidak memiliki akses halaman ini"
		>
			<template #footer>
				<n-button @click="refresh">Refresh</n-button>
			</template>
		</n-result>
		<template v-else>
			<template v-if="fetchStatus === 'success'">
				<div
					ref="bgImage"
					:class="['relative bg-center bg-cover', isFullscreen ? 'w-full h-full' : 'w-full pb-[56.25%]']"
					:style="{ backgroundImage: `url(${urlBackground})` }"
				>
					<!-- Dark overlay -->
					<div class="absolute inset-0 bg-black bg-opacity-50 z-10"></div>

					<!-- Content on top of the dark overlay -->
					<div class="absolute inset-0 flex z-20 text-white text-2xl">
						<!-- Left div with 50% width, centered content -->
						<div class="w-1/2 h-full flex items-center pl-9">
							<UseDraggable
								storage-key="vueuse-draggable-list"
								storage-type="session"
								:initial-value="{ x: '50%', y: '50%' }"
								style="position: absolute"
							>
								<div>
									<h6
										:style="{
											fontSize: `${greetingObject.listSize + 4}px`,
											fontFamily: greetingObject.listFont
										}"
									>
										Kehadiran:
									</h6>

									<div
										v-if="guestsData"
										v-auto-animate="{ duration: 1000 }"
										class="divide-y divide-white"
									>
										<div v-for="guest in guestsData" :key="guest.id" class="flex flex-col pb-3">
											<n-p
												class="mt-2 mb-0 leading-none text-white font-normal"
												depth="1"
												:style="{
													fontSize: `${greetingObject.listSize}px`,
													fontFamily: greetingObject.listFont
												}"
											>
												{{ guest.name }}
												<n-tag
													v-if="guest.level > 1"
													class="font-bold pa-0 leading-none"
													type="warning"
													:bordered="false"
													:style="{ fontSize: `${greetingObject.listSize - 2}px` }"
												>
													{{ translateLevel(guest.level) }}
												</n-tag>
											</n-p>
											<n-p
												class="mt-0 mb-0 leading-6 text-[#acb5be] font-normal"
												depth="3"
												:style="{
													fontSize: `${greetingObject.listSize - 4}px`,
													fontFamily: greetingObject.listFont
												}"
											>
												{{ guest.status_relation ?? "-" }}
											</n-p>
										</div>
									</div>
								</div>
							</UseDraggable>
						</div>

						<!-- Right div with 50% width, split into two vertical sections -->
						<div class="w-1/2 h-full flex flex-col">
							<div class="flex-1 flex items-center justify-center">
								<UseDraggable
									storage-key="vueuse-draggable-title"
									storage-type="session"
									:initial-value="{ x: '50%', y: '50%' }"
									style="position: absolute"
								>
									<h1
										:key="`title-${guestKey}`"
										class="text-white font-normal"
										:style="`font-size: ${greetingObject.titleSize}px; font-family: ${greetingObject.titleFont}`"
									>
										{{ agendaData.title }}
									</h1>
								</UseDraggable>
							</div>
							<div class="flex-1 flex items-center justify-center">
								<UseDraggable
									storage-key="vueuse-draggable-guest"
									storage-type="session"
									:initial-value="{ x: '50%', y: '50%' }"
									style="position: absolute"
								>
									<n-flex
										ref="elGuest"
										vertical
										justify="center"
										align="center"
										class="fade-in-bottom"
										:key="`guest-${guestKey}`"
										:style="`font-size: ${greetingObject.guestSize}px; font-family: ${greetingObject.guestFont}`"
									>
										<n-space>
											<h3
												class="underline text-white font-normal"
												:style="{
													fontSize: `${greetingObject.guestSize}px`,
													fontFamily: greetingObject.guestFont
												}"
											>
												{{ displayGuest?.name }}
											</h3>
											<span
												v-if="displayGuest?.level > 1"
												:class="
													displayGuest?.level > 1 ? 'text-[#FFD700] text-white' : 'text-white'
												"
											>
												<n-tag class="font-bold" type="warning" :bordered="false">
													{{ translateLevel(displayGuest?.level) }}
												</n-tag>
											</span>
										</n-space>

										<h5
											class="text-center text-white font-normal"
											:style="{
												fontSize: `${greetingObject.guestSize}px`,
												fontFamily: greetingObject.guestFont
											}"
										>
											{{ displayGuest?.status_relation }}
										</h5>
									</n-flex>
								</UseDraggable>
							</div>
						</div>
					</div>
				</div>

				<n-h1 prefix="bar">
					<h1>LAYAR SAPA</h1>
				</n-h1>

				<n-card title="Setting">
					<n-space vertical>
						<n-space align="center">
							<n-button type="primary" @click="toggleFullscreen">
								<template #icon>
									<Icon :name="OpenIcon" />
								</template>
								fullscreen
							</n-button>
							<n-button circle @click="refresh">
								<template #icon>
									<Icon :name="ReloadIcon" />
								</template>
							</n-button>
							<n-popselect v-model:value="selectedPubChannel" :options="channelOption">
								<n-button circle>
									<template #icon>
										<Icon name="ph:broadcast" />
									</template>
								</n-button>
							</n-popselect>
							<n-switch v-model:value="vipOnly">
								<template #checked>VIP</template>
								<template #unchecked>all</template>
							</n-switch>
						</n-space>
						<n-form class="mt-4" ref="settingRef">
							<p class="mb-4">Ukuran:</p>
							<n-space>
								<n-form-item label="Daftar" path="listSize">
									<n-input-number v-model:value="greetingObject.listSize" :min="1" clearable />
								</n-form-item>
								<n-form-item label="Judul" path="titleSize">
									<n-input-number v-model:value="greetingObject.titleSize" :min="1" clearable />
								</n-form-item>
								<n-form-item label="Tamu" path="guestSize">
									<n-input-number v-model:value="greetingObject.guestSize" :min="1" clearable />
								</n-form-item>
							</n-space>
							<n-space>
								<n-form-item label="Font daftar">
									<UniversalFontPicker
										v-model="greetingObject.listFont"
										:min="1"
										clearable
										style="width: 300px"
									/>
								</n-form-item>
								<n-form-item label="Font judul">
									<UniversalFontPicker
										v-model="greetingObject.titleFont"
										:min="1"
										clearable
										style="width: 300px"
									/>
								</n-form-item>
								<n-form-item label="Font tamu">
									<UniversalFontPicker
										v-model="greetingObject.guestFont"
										:min="1"
										clearable
										style="width: 300px"
									/>
								</n-form-item>
							</n-space>
						</n-form>
					</n-space>
				</n-card>
			</template>
		</template>
	</div>
</template>

<script setup>
import {
	NButton,
	useMessage,
	useLoadingBar,
	NResult,
	NH1,
	NSpace,
	NCard,
	NForm,
	NFormItem,
	NInputNumber,
	NPopselect,
	NTag,
	NList,
	NSwitch,
	NP
} from "naive-ui"
import _includes from "lodash/includes"
import { useMainStore } from "@/stores/main"
import { useFullscreen } from "@vueuse/core"
import { UseDraggable } from "@vueuse/components"
import UniversalFontPicker from "@formester/universal-font-picker"
import "@formester/universal-font-picker/dist/universal-font-picker.css"

definePageMeta({
	name: "Greetings",
	title: "Greetings"
})

const OpenIcon = "fluent:full-screen-maximize-24-regular"
const ReloadIcon = "mdi:reload"

const elGuest = ref(null)

const route = useRoute()
const user = useDirectusUser()
const { getItemById } = useDirectusItems()
const message = useMessage()
const loadingBar = useLoadingBar()
const { getItems } = useDirectusItems()
const mainStore = useMainStore()
const selectedPubChannel = useSelectedPubChannel()
const greetingObject = useGreetingObject()
const ably = useNuxtApp().$ably

const fetchStatus = ref("idle")
const listAccess = ref([])
const agendaData = ref(null)
const guestsData = ref(null)
const guestsMeta = ref(null)
const isAdmin = ref(user.value.role.name === "Administrator")
const haveAccess = ref(true)
const messageSub = ref(null)

const bgImage = ref(null) //ref of greeting screen
const { isFullscreen, toggle } = useFullscreen(bgImage)
const displayGuest = ref(null)
const displayGuestsList = ref(null)
const guestKey = ref(0)
const vipOnly = ref(false)

const channelOption = ref([
	{
		label: "global",
		value: "global"
	},
	{
		label: "usher",
		value: "usher"
	}
])

const toggleFullscreen = () => {
	toggle()
}

const fetchParam = ref({
	filter: {
		event: {
			_eq: route.params.id
		},
		presence: {
			_eq: true
		}
	},
	sort: ["-attendance_time"],
	search: "",
	fields: ["*"],
	meta: "*",
	limit: 10,
	page: 1
})

const fetchAgenda = async () => {
	loadingBar.start()
	fetchStatus.value = "pending"
	try {
		const item = await getItemById({
			collection: "event",
			id: route.params.id,
			params: {
				fields: [
					"id",
					"title",
					"greeting_background",
					"users.directus_users_id",
					"users.directus_users_id.id",
					"users.directus_users_id.first_name",
					"users.directus_users_id.last_name"
				]
			}
		})
		console.log(item.users)
		item.users.forEach(user => {
			listAccess.value.push(user.directus_users_id.id)
		})
		agendaData.value = item
		fetchStatus.value = "success"
		loadingBar.finish()
	} catch (e) {
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
		} else {
			console.error(e)
			message.error(e)
		}
		fetchStatus.value = "error"
		loadingBar.error()
	}
}

const fetchGuests = async () => {
	// fetchStatus.value = "pending"
	loadingBar.start()
	try {
		if (vipOnly.value) {
			fetchParam.value.filter.level = {
				_gt: 1
			}
		} else {
			delete fetchParam.value.filter.level
		}
		const items = await getItems({
			collection: "guest",
			params: {
				...fetchParam.value
			}
		})
		guestsData.value = items.data
		guestsMeta.value = items.meta
		// fetchStatus.value = "success"
		loadingBar.finish()
	} catch (e) {
		loadingBar.error()
		// fetchStatus.value = "error"
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
		} else {
			console.error(e)
			message.error(e)
		}
	} finally {
		guestKey.value++
	}
}

const refresh = e => {
	mainStore.softReload()
	return e
}

const urlBackground = computed(
	() =>
		agendaData.value?.greeting_background ||
		"https://directus.bagimomen.my.id/assets/2f648530-7a19-4f74-adb1-bebd1daef911/default_bg.jpg"
)

onMounted(async () => {
	await fetchAgenda()

	let channelName = `${agendaData.value?.id}`
	if (selectedPubChannel.value != "global") {
		channelName = `${agendaData.value?.id}-${user.value.id}`
		fetchParam.value.filter.regist_by = {
			_eq: user.value.id
		}
	} else {
		delete fetchParam.value.filter.regist_by
	}
	const channel = ably.channels.get(channelName)
	channel.subscribe(async msg => {
		messageSub.value = msg
		if (msg.name == "greeting") {
			displayGuest.value = msg.data
			await fetchGuests()
			guestKey.value++
			console.log(msg)
		}
	})

	if (!isAdmin.value) {
		haveAccess.value = _includes(listAccess.value, user.value.id) ? true : false
	}
})
</script>

<style lang="scss" scoped>
.fade-in-bottom {
	animation: fade-in-bottom 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}

/* ----------------------------------------------
 * Generated by Animista on 2024-9-12 15:9:41
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

@keyframes fade-in-bottom {
	0% {
		transform: translateY(50px);
		opacity: 0;
	}
	100% {
		transform: translateY(0);
		opacity: 1;
	}
}

::v-deep .universal-font-picker {
	background-color: white;
	color: black;
}
</style>
