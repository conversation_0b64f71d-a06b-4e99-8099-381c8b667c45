import { DirectusItems } from "nuxt-directus/dist/runtime/types/"
import { type Event } from "~/types/globals"
const { getItemById } = useDirectusItems()

export const useGetAgendaById = () => {
	const data = ref(null)
	const loading = ref(false)

	const load = async (id: string) => {
		loading.value = true
		const items: DirectusItems = await getItemById<Event>({
			collection: "event",
			id
		})
		data.value = items
		loading.value = false
	}

	return { data, load, loading }
}
