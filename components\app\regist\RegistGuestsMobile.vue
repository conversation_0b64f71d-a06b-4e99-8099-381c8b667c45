<template>
	<n-card content-style="padding: 0px;">
		<template v-for="(guest, index) in guests" :key="guest.id">
			<div v-wave class="flex items-center p-4" @click="emitSelect(guest)">
				<n-flex vertical>
					<n-space vertical style="font-size: 12px">
						{{ guest.name }}
						<n-text depth="3">{{ guest.status_relation || "-" }}</n-text>
						<n-text depth="3">
							<data class="flex items-center">
								<span :class="guest.level > 1 ? 'text-[#FFD700]' : ''">
									{{ translateLevel(guest.level) }}
								</span>
								|
								{{ guest.presence ? "Hadir" : "Belum Hadir" }}
								|
								{{ guest.amount_guest }} Pax
								<n-tag
									v-if="guest.presence && guest.on_Site"
									class="!ml-2"
									round
									:bordered="false"
									size="small"
									type="success"
								>
									On Site
									<template #icon>
										<Icon name="icon-park-solid:check-one" />
									</template>
								</n-tag>
								<n-tag
									v-if="guest.presence && !guest.on_Site"
									class="!ml-2"
									round
									:bordered="false"
									size="small"
									type="success"
								>
									Hadir
									<template #icon>
										<Icon name="icon-park-solid:check-one" />
									</template>
								</n-tag>
							</data>
						</n-text>
					</n-space>
				</n-flex>
			</div>
			<n-divider v-if="index < guests.length - 1" class="!my-2" />
		</template>
	</n-card>
</template>

<script setup>
import { NCard, NTag, NFlex, NSpace, NText, NDivider } from "naive-ui"
defineProps({
	guests: Array
})
const emits = defineEmits(["select"])
const emitSelect = guest => {
	emits("select", guest)
}
</script>

<style></style>
