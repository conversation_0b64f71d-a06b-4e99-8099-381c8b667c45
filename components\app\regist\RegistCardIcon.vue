<template>
	<n-card>
		<div class="flex items-center h-full">
			<div class="card-wrap flex gap-4 items-center text-center flex-col">
				<div class="icon">
					<div class="icon boxed" style="--size: 50px">
						<div class="bg"></div>
						<Icon :size="30" :name="icon" />
					</div>
				</div>
				<div class="info flex flex-col">
					<div class="value">{{ value }}</div>
					<div class="title">{{ title }}</div>
				</div>
			</div>
		</div>
	</n-card>
</template>

<script setup>
import { NCard } from "naive-ui"
defineProps({
	value: Number,
	title: String,
	icon: String
})
</script>

<style scoped lang="scss">
.n-card {
	.card-wrap {
		width: 100%;

		.title {
			font-size: 18px;
			word-break: initial;
		}
		.value {
			font-family: var(--font-family-display);
			font-size: 22px;
			font-weight: bold;
			margin-bottom: 6px;
		}
	}
}
.icon {
	color: var(--primary-color);
	width: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;

	&.boxed {
		height: 50px;

		.bg {
			background-color: var(--primary-color);
			opacity: 0.1;
			position: absolute;
			top: 0;
			left: 0;
			border-radius: 50%;
			width: 100%;
			height: 100%;
		}
	}
}
</style>
