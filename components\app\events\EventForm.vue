<template>
	<component
		:is="viewport.isLessThan('tablet') ? NDrawer : NModal"
		v-model:show="localDrawerEvent"
		:class="viewport.isLessThan('tablet') ? `rounded-none` : ``"
		:width="viewport.isLessThan('tablet') ? '100%' : ''"
		:placement="viewport.isLessThan('tablet') ? 'right' : ''"
		@after-leave="resetModel"
	>
		<component
			:is="viewport.isLessThan('tablet') ? NDrawerContent : NCard"
			:title="viewport.isGreaterOrEquals('tablet') ? drawerTitle : ''"
			:size="viewport.isGreaterOrEquals('tablet') ? 'medium' : ''"
			:closable="viewport.isGreaterOrEquals('tablet')"
			:style="{ maxWidth: viewport.isGreaterOrEquals('tablet') ? '600px' : '100%' }"
			@close="handleBack"
		>
			<template v-if="viewport.isLessThan('tablet')" #header>
				<n-page-header @back="handleBack">
					<template #title>
						{{ drawerTitle }}
					</template>
				</n-page-header>
			</template>

			<!-- <n-form ref="formRef" :rules="rules" :model="localModel" @keydown.enter.prevent="handleSubmitEvent"> -->
			<n-form ref="formRef" :rules="rules" :model="localModel">
				<n-form-item path="title" label="Judul">
					<n-input
						v-model:value="localModel.title"
						id="title"
						name="title"
						type="text"
						placeholder="masukkan judul event"
					/>
				</n-form-item>

				<n-form-item path="idEvent" label="ID Event">
					<n-input
						v-model:value="localModel.idEvent"
						id="id-event"
						name="id-event"
						type="text"
						placeholder="masukkan ID event"
					/>
				</n-form-item>

				<n-form-item path="lokasi_event" label="Lokasi">
					<n-input
						v-model:value="localModel.lokasi_event"
						id="location"
						name="location"
						type="text"
						placeholder="masukkan lokasi event"
					/>
				</n-form-item>

				<n-form-item path="link" label="link">
					<n-input
						v-model:value="localModel.link"
						id="link"
						name="link"
						type="text"
						placeholder="masukkan link event"
					/>
				</n-form-item>

				<n-form-item path="time_start" label="Waktu Event">
					<n-date-picker
						v-model:formatted-value="localModel.time_start"
						id="time-start"
						name="time-start"
						value-format="yyyy-MM-dd HH:mm:ss"
						type="datetime"
						placeholder="masukkan tanggal acara"
						clearable
					/>
				</n-form-item>
				<n-form-item label="Layar Sapa" path="screenGreet">
					<n-switch v-model:value="localModel.screenGreet" id="screen-greet" name="screen-greet" />
				</n-form-item>
				<n-form-item v-if="localModel.screenGreet" path="background_event" label="Link Background">
					<n-input
						v-model:value="localModel.background_event"
						id="background-event"
						name="background-event"
						type="textarea"
						placeholder="masukkan link background"
					/>
				</n-form-item>
				<n-form-item label="Cetak" path="print">
					<n-switch v-model:value="localModel.print" id="print" name="print" />
				</n-form-item>
				<n-form-item label="Akses" path="users">
					<n-select
						v-model:value="localModel.users"
						id="users"
						name="users"
						:options="usersOptions"
						multiple
						filterable
						placeholder="pilih username"
					/>
				</n-form-item>
			</n-form>

			<template #footer>
				<n-button block type="primary" :loading="loadingSubmit" @click="handleSubmitEvent">
					{{ drawerAction }}
				</n-button>
			</template>
		</component>
	</component>
</template>

<script setup>
import {
	NDrawer,
	NModal,
	NDrawerContent,
	NCard,
	NPageHeader,
	NForm,
	NFormItem,
	NInput,
	NDatePicker,
	NSwitch,
	NSelect,
	NButton
} from "naive-ui"

const props = defineProps({
	drawerTitle: String,
	drawerAction: String,
	formType: String,
	model: Object,
	loadingSubmit: Boolean,
	drawerEvent: Boolean,
	usersOptions: Array
})

const emit = defineEmits(["handleBack", "handleSubmitEvent", "update:drawerEvent", "resetModel"])

const viewport = useViewport()

const formRef = ref(null)

// Create a local copy of the model and drawerEvent to avoid mutating the prop directly
const localModel = reactive({ ...props.model })
const localDrawerEvent = ref(props.drawerEvent)

watch(
	() => props.model,
	newModel => {
		Object.assign(localModel, newModel)
	},
	{ deep: true }
)

watch(
	() => props.drawerEvent,
	newDrawerEvent => {
		localDrawerEvent.value = newDrawerEvent
	}
)

watch(localDrawerEvent, newDrawerEvent => {
	emit("update:drawerEvent", newDrawerEvent)
})

const rules = {
	title: {
		required: true,
		message: "Judul tidak boleh kosong",
		trigger: ["input", "blur"]
	},
	idEvent: {
		required: true,
		message: "ID Event tidak boleh kosong",
		trigger: ["input", "blur"]
	}
}

const handleBack = () => {
	emit("handleBack")
}

const handleSubmitEvent = e => {
	e.preventDefault()
	formRef.value?.validate(errors => {
		if (!errors) {
			emit("handleSubmitEvent", localModel)
		} else {
			console.error(errors)
		}
	})
}

const resetModel = () => {
	emit("resetModel")
}
</script>

<style scoped></style>
