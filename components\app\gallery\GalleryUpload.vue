<template>
  <n-modal
    v-model:show="isVisible"
    preset="card"
    title="Upload Images"
    style="max-width: 600px; width: 100%;"
    :mask-closable="true"
  >
    <n-form
      ref="formRef"
      :model="formModel"
      :rules="rules"
      label-placement="left"
      label-width="80"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="Title" path="title">
        <n-input v-model:value="formModel.title" placeholder="Enter image title" />
      </n-form-item>
      
      <n-form-item label="Description" path="description">
        <n-input
          v-model:value="formModel.description"
          type="textarea"
          placeholder="Enter image description"
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </n-form-item>
      
      <n-form-item label="Image" path="image">
        <n-upload
          ref="uploadRef"
          :default-upload="false"
          :max="1"
          :accept="'image/*'"
          :show-file-list="true"
          list-type="image-card"
          @change="handleUploadChange"
        >
          <n-upload-dragger>
            <div class="flex flex-col items-center justify-center py-4">
              <Icon name="carbon:image" :size="48" class="text-gray-400" />
              <p class="mt-2 text-sm text-gray-500">Click or drag an image to upload</p>
            </div>
          </n-upload-dragger>
        </n-upload>
      </n-form-item>
      
      <div class="flex justify-end space-x-4 mt-4">
        <n-button @click="isVisible = false">Cancel</n-button>
        <n-button type="primary" :loading="uploading" @click="handleSubmit">Upload</n-button>
      </div>
    </n-form>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { NModal, NForm, NFormItem, NInput, NUpload, NUploadDragger, NButton } from 'naive-ui'
import Icon from '~/components/common/Icon.vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  eventId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:show', 'upload-success'])

const config = useRuntimeConfig()
const { token } = useDirectusToken()
const message = useMessage()

const isVisible = ref(props.show)
const uploading = ref(false)
const formRef = ref(null)
const uploadRef = ref(null)
const fileList = ref([])

// Form model
const formModel = ref({
  title: '',
  description: '',
  image: null,
  event: props.eventId
})

// Form validation rules
const rules = {
  title: {
    required: true,
    message: 'Please enter a title',
    trigger: 'blur'
  },
  image: {
    required: true,
    message: 'Please upload an image',
    trigger: 'change'
  }
}

// Watch for external show changes
watch(() => props.show, (newVal) => {
  isVisible.value = newVal
})

// Watch for internal show changes
watch(isVisible, (newVal) => {
  emit('update:show', newVal)
  if (!newVal) {
    // Reset form when modal is closed
    resetForm()
  }
})

// Handle file upload change
const handleUploadChange = (options) => {
  fileList.value = options.fileList
  formModel.value.image = options.file
}

// Reset the form
const resetForm = () => {
  formModel.value = {
    title: '',
    description: '',
    image: null,
    event: props.eventId
  }
  fileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clear()
  }
  if (formRef.value) {
    formRef.value.restoreValidation()
  }
}

// Handle form submission
const handleSubmit = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return
    
    if (!formModel.value.image) {
      message.error('Please upload an image')
      return
    }
    
    uploading.value = true
    
    try {
      // First upload the image file to Directus
      const formData = new FormData()
      formData.append('file', formModel.value.image.file)
      
      const imageResponse = await $fetch(`${config.public.directusUrl}/files`, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${token.value}`
        }
      })
      
      // Then create the gallery item with the image reference
      const galleryItem = {
        title: formModel.value.title,
        description: formModel.value.description,
        image: imageResponse.data.id,
        event: props.eventId
      }
      
      await $fetch(`${config.public.directusUrl}/items/gallery`, {
        method: 'POST',
        body: galleryItem,
        headers: {
          Authorization: `Bearer ${token.value}`,
          'Content-Type': 'application/json'
        }
      })
      
      message.success('Image uploaded successfully')
      emit('upload-success')
      isVisible.value = false
    } catch (error) {
      console.error('Upload error:', error)
      message.error('Failed to upload image')
    } finally {
      uploading.value = false
    }
  })
}
</script>
