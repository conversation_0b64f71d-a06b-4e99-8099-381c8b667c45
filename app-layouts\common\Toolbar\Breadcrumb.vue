<template>
	<n-breadcrumb class="breadcrumb">
		<n-breadcrumb-item @click="goto({ path: '/' })">
			<Icon :size="16" :name="HomeIcon" />
			Acara
		</n-breadcrumb-item>
		<TransitionGroup name="anim">
			<template v-for="(item, index) of items" :key="item.key">
				<n-breadcrumb-item
					v-if="!_includes(hideList, item.path) && !isNumeric(item.path)"
					:clickable="false"
					:class="`index-${index}`"
					@click="goto(item)"
				>
					<Icon v-if="item.path == 'dashboard'" :size="16" :name="DashboardIcon" />
					<Icon v-if="item.path == 'guests'" :size="16" :name="GuestsIcon" />
					<Icon v-if="item.path == 'registration'" :size="16" :name="RegistIcon" />
					<Icon v-if="item.path == 'greetings'" :size="16" :name="ScreenIcon" />
					<Icon v-if="item.path == 'sender'" :size="16" :name="SenderIcon" />
					{{ item.name }}
				</n-breadcrumb-item>
			</template>
		</TransitionGroup>
	</n-breadcrumb>
</template>

<script setup>
import { NBreadcrumb, NBreadcrumbItem } from "naive-ui"
import _upperCase from "lodash/upperCase"
import _capitalize from "lodash/capitalize"
import _split from "lodash/split"
import _includes from "lodash/includes"
import { onBeforeMount, ref } from "vue"
import { useRouter, useRoute } from "vue-router"
import Icon from "@/components/common/Icon.vue"

const HomeIcon = "carbon:home"
// const EventIcon = "carbon:event";
const DashboardIcon = "carbon:dashboard"
const GuestsIcon = "carbon:user-multiple"
const RegistIcon = "carbon:mobile-check"
const ScreenIcon = "carbon:screen"
const SenderIcon = "carbon:send-alt"

defineOptions({
	name: "Breadcrumb"
})

const router = useRouter()
const route = useRoute()

const items = ref([])
const hideList = ref(["events"])

function goto(page) {
	if (page.name && page.name !== route.name) {
		router.push({ name: page.name })
		return
	}
	if (page.path && page.path !== route.path) {
		router.push({ path: page.path })
		return
	}
}

function checkRoute(route) {
	const newItems = []
	const pathChunks = route?.path?.indexOf("/") !== -1 ? _split(route?.path || "", "/") : [route?.path]

	for (const chunk of pathChunks) {
		if (chunk) {
			const name = _capitalize(_upperCase(chunk))
			const path = chunk.toLowerCase()

			newItems.push({
				name,
				path,
				key: name + path
			})
		}
	}

	if (JSON.stringify(items.value) !== JSON.stringify(newItems)) {
		items.value = newItems
	}
}

const isNumeric = str => {
	return !isNaN(str) && !isNaN(parseFloat(str))
}

onBeforeMount(() => {
	checkRoute(router.currentRoute.value)

	router.beforeResolve(route => {
		checkRoute(route)
	})
})
</script>

<style lang="scss" scoped>
.breadcrumb {
	.anim-move,
	.anim-enter-active {
		transition: all 0.5s var(--bezier-ease);

		@for $i from 0 through 10 {
			&.index-#{$i} {
				transition-delay: $i * 0.1s;
			}
		}
	}

	.anim-leave-active {
		display: none;
	}

	.anim-enter-from {
		opacity: 0;
		transform: translateX(-5px);
	}
}
</style>
