<template>
	<div>
		<div>
			<n-ellipsis expand-trigger="click" line-clamp="1">
				<n-text class="my-0 text-sm leading-none" depth="1">{{ guest.name }}</n-text>
			</n-ellipsis>
			<Icon v-if="guest.level > 1" name="openmoji:star" :size="20" />
			<Icon v-if="guest.level > 2" name="openmoji:star" :size="20" />
		</div>
		<div>
			<n-ellipsis expand-trigger="click" line-clamp="1">
				<n-p class="my-0 text-sm leading-none" depth="3">{{ guest.status_relation ?? "-" }}</n-p>
			</n-ellipsis>
		</div>
	</div>
</template>

<script setup>
import { NP, NEllipsis, NSpace } from "naive-ui"
defineProps({
	guest: Object
})
</script>

<style></style>
