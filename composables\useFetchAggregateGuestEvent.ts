export const useFetchAggregateGuestEvent = (eventID: string | number) => {
	const { $directus } = useNuxtApp()
	interface AggregateGuestEvent {
		[key: string]: any
	}

	const aggregateGuestEvent = ref<AggregateGuestEvent | null>(null)

	// Initial structure of aggregatedGuestEvent
	const initialAggregatedGuestEvent = {
		invitation: {
			total: 0,
			regular: 0,
			vip: 0,
			vvip: 0,
			attend: {
				total: 0,
				regular: 0,
				vip: 0,
				vvip: 0
			},
			absent: {
				total: 0,
				regular: 0,
				vip: 0,
				vvip: 0
			}
		},
		guest: {
			total: 0,
			regular: 0,
			vip: 0,
			vvip: 0,
			attend: {
				total: 0,
				regular: 0,
				vip: 0,
				vvip: 0
			},
			absent: {
				total: 0,
				regular: 0,
				vip: 0,
				vvip: 0
			}
		},
		gift: {
			total: 0,
			cash: 0,
			present: 0,
			transfer: 0
		}
	}

	// Creating the reactive state
	const aggregatedGuestEvent = reactive({ ...initialAggregatedGuestEvent })

	// Properly reset aggregatedGuestEvent by clearing all its properties
	const resetAggregatedGuestEvent = () => {
		// Reset all fields back to the initial values
		Object.assign(aggregatedGuestEvent, JSON.parse(JSON.stringify(initialAggregatedGuestEvent)))
	}

	const setTotal = () => {
		// Aggregate calculations as per your existing logic...
		for (const key in aggregateGuestEvent.value) {
			if (key !== "gifts") {
				for (const entry of aggregateGuestEvent.value[key]) {
					const countId = entry.count.id
					const amountGuest = entry.sum?.amount_guest || 0

					aggregatedGuestEvent.invitation.total += countId
					aggregatedGuestEvent.guest.total += amountGuest

					if (key.includes("attend")) {
						aggregatedGuestEvent.invitation.attend.total += countId
						aggregatedGuestEvent.guest.attend.total += amountGuest
					} else if (key.includes("absent")) {
						aggregatedGuestEvent.invitation.absent.total += countId
						aggregatedGuestEvent.guest.absent.total += amountGuest
					}
				}
			} else {
				// Handle gifts
				for (const gift of aggregateGuestEvent.value.gifts) {
					const countId = gift.count.id
					const giftGroup = gift.group.gift

					if (giftGroup && giftGroup.includes("1")) {
						aggregatedGuestEvent.gift.cash += countId
					}
					if (giftGroup && giftGroup.includes("2")) {
						aggregatedGuestEvent.gift.present += countId
					}
					if (giftGroup && giftGroup.includes("3")) {
						aggregatedGuestEvent.gift.transfer += countId
					}
				}
			}
		}

		// Final total calculations
		aggregatedGuestEvent.gift.total =
			aggregatedGuestEvent.gift.cash + aggregatedGuestEvent.gift.present + aggregatedGuestEvent.gift.transfer

		// Set specific totals based on attendance and guest level
		aggregatedGuestEvent.invitation.attend.regular = aggregateGuestEvent.value?.attendRegular[0]?.count.id || 0
		aggregatedGuestEvent.invitation.absent.regular = aggregateGuestEvent.value?.absentRegular[0]?.count.id || 0
		aggregatedGuestEvent.invitation.regular =
			aggregatedGuestEvent.invitation.attend.regular + aggregatedGuestEvent.invitation.absent.regular

		aggregatedGuestEvent.invitation.attend.vip = aggregateGuestEvent.value?.attendVip[0]?.count.id || 0
		aggregatedGuestEvent.invitation.absent.vip = aggregateGuestEvent.value?.absentVip[0]?.count.id || 0
		aggregatedGuestEvent.invitation.vip =
			aggregatedGuestEvent.invitation.attend.vip + aggregatedGuestEvent.invitation.absent.vip

		aggregatedGuestEvent.invitation.attend.vvip = aggregateGuestEvent.value?.attendVvip[0]?.count.id || 0
		aggregatedGuestEvent.invitation.absent.vvip = aggregateGuestEvent.value?.absentVvip[0]?.count.id || 0
		aggregatedGuestEvent.invitation.vvip =
			aggregatedGuestEvent.invitation.attend.vvip + aggregatedGuestEvent.invitation.absent.vvip

		aggregatedGuestEvent.guest.attend.regular = aggregateGuestEvent.value?.attendRegular[0]?.sum?.amount_guest || 0
		aggregatedGuestEvent.guest.absent.regular = aggregateGuestEvent.value?.absentRegular[0]?.sum?.amount_guest || 0
		aggregatedGuestEvent.guest.regular =
			aggregatedGuestEvent.guest.attend.regular + aggregatedGuestEvent.guest.absent.regular

		aggregatedGuestEvent.guest.attend.vip = aggregateGuestEvent.value?.attendVip[0]?.sum?.amount_guest || 0
		aggregatedGuestEvent.guest.absent.vip = aggregateGuestEvent.value?.absentVip[0]?.sum?.amount_guest || 0
		aggregatedGuestEvent.guest.vip = aggregatedGuestEvent.guest.attend.vip + aggregatedGuestEvent.guest.absent.vip

		aggregatedGuestEvent.guest.attend.vvip = aggregateGuestEvent.value?.attendVvip[0]?.sum?.amount_guest || 0
		aggregatedGuestEvent.guest.absent.vvip = aggregateGuestEvent.value?.absentVvip[0]?.sum?.amount_guest || 0
		aggregatedGuestEvent.guest.vvip =
			aggregatedGuestEvent.guest.attend.vvip + aggregatedGuestEvent.guest.absent.vvip
	}

	const loading = ref(false)

	const load = async () => {
		loading.value = true
		const { token } = useDirectusToken()
		await $directus.setToken(token.value)
		const result = await $directus.query(
			`
      query getGuestSummaries($id: GraphQLStringOrFloat) {
        attendRegular: guest_aggregated(
          filter: { event: { id: { _eq: $id } }, presence: { _eq: true }, level: { _eq: 1 } }
        ) {
          count {
            id
          }
          sum {
            amount_guest
          }
        }
        attendVip: guest_aggregated(
          filter: { event: { id: { _eq: $id } }, presence: { _eq: true }, level: { _eq: 2 } }
        ) {
          count {
            id
          }
          sum {
            amount_guest
          }
        }
        attendVvip: guest_aggregated(
          filter: { event: { id: { _eq: $id } }, presence: { _eq: true }, level: { _eq: 3 } }
        ) {
          count {
            id
          }
          sum {
            amount_guest
          }
        }
        absentRegular: guest_aggregated(
          filter: { event: { id: { _eq: $id } }, presence: { _eq: false }, level: { _eq: 1 } }
        ) {
          count {
            id
          }
          sum {
            amount_guest
          }
        }
        absentVip: guest_aggregated(
          filter: { event: { id: { _eq: $id } }, presence: { _eq: false }, level: { _eq: 2 } }
        ) {
          count {
            id
          }
          sum {
            amount_guest
          }
        }
        absentVvip: guest_aggregated(
          filter: { event: { id: { _eq: $id } }, presence: { _eq: false }, level: { _eq: 3 } }
        ) {
          count {
            id
          }
          sum {
            amount_guest
          }
        }
        gifts: guest_aggregated(filter: { event: { id: { _eq: $id } } }, groupBy: "gift") {
          group
          count {
            id
          }
        }
      }
      `,
			{ id: eventID }
		)

		aggregateGuestEvent.value = result

		// Reset and update totals
		resetAggregatedGuestEvent()
		setTotal()

		loading.value = false
	}

	return { load, aggregateGuestEvent, aggregatedGuestEvent, resetAggregatedGuestEvent }
}
