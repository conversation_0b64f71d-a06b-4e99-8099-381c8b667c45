<template>
	<div class="page">
		<n-card class="header flex flex-col" content-style="padding:0">
			<div class="user-info flex flex-wrap">
				<div class="propic">
					<Avatar :size="100" variant="beam" :name="user?.username" />
					<!-- <ImageCropper
						v-slot="{ openCropper }"
						@crop="setCroppedImage"
						:placeholder="'Select your profile picture'"
					>
						<Icon :name="EditIcon" :size="16" class="edit" @click="openCropper()"></Icon>
					</ImageCropper> -->
				</div>
				<div class="info grow flex flex-col justify-center">
					<div class="name">
						<h1>{{ user?.first_name }} {{ user?.last_name }}</h1>
					</div>
					<div class="details flex flex-wrap">
						<div class="item">
							<n-tooltip placement="top">
								<template #trigger>
									<div class="tooltip-wrap">
										<Icon :name="MailIcon"></Icon>
										<span>{{ user?.email }}</span>
									</div>
								</template>
								<span>Contacts</span>
							</n-tooltip>
						</div>
					</div>
				</div>
			</div>
		</n-card>
	</div>
</template>

<script setup>
import { NTooltip, NCard } from "naive-ui"
import Icon from "@/components/common/Icon.vue"
import Avatar from "vue-boring-avatars"

const MailIcon = "tabler:mail"

const user = useDirectusUser()
</script>

<style lang="scss" scoped>
.page {
	.header {
		.user-info {
			gap: 30px;
			padding: 30px;
			padding-bottom: 20px;
			border-block-end: var(--border-small-050);
			container-type: inline-size;

			.propic {
				position: relative;
				height: 100px;

				.edit {
					display: none;
					align-items: center;
					justify-content: center;
					background-color: var(--primary-color);
					color: var(--bg-color);
					position: absolute;
					width: 26px;
					height: 26px;
					border-radius: 50%;
					top: -1px;
					right: -1px;
					border: 1px solid var(--bg-color);
					cursor: pointer;
				}
			}
			.info {
				.name {
					margin-bottom: 12px;

					@media (max-width: 450px) {
						h1 {
							font-size: 28px;
						}
					}
				}

				.details {
					gap: 24px;

					.item {
						.tooltip-wrap {
							display: flex;
							align-items: center;

							span {
								line-height: 1;
								margin-left: 8px;
							}
						}
					}
				}
			}

			@container (max-width: 900px) {
				.propic {
					.edit {
						display: flex;
					}
				}
				.actions {
					display: none;
				}
			}
		}
		.section-selector {
			padding: 0px 30px;
			padding-top: 15px;

			:deep() {
				.n-tabs .n-tabs-tab {
					padding-bottom: 20px;
				}
			}
		}
	}

	.main {
		margin-top: 18px;
	}
}
</style>
