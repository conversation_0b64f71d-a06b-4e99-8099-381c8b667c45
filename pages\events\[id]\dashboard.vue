<template>
	<div class="page">
		<n-result
			v-if="!haveAccess"
			status="warning"
			title="Aks<PERSON> ditolak"
			description="anda tidak memiliki akses halaman ini"
		>
			<template #footer>
				<n-button @click="refresh">Refresh</n-button>
			</template>
		</n-result>
		<template v-else>
			<n-result v-if="fetchError" status="error" title="Error" description="<PERSON><PERSON><PERSON><PERSON>">
				<template #footer>
					<n-button @click="refresh">Refresh</n-button>
				</template>
			</n-result>

			<n-flex v-if="fetchStatus === 'idle'" size="large" vertical>
				<n-skeleton height="220px" width="100%" />
				<n-flex size="large" :vertical="viewport.isLessThan('tablet')">
					<n-skeleton height="200px" :width="viewport.isLessThan('tablet') ? '100%' : '31%'" />
					<n-skeleton height="200px" :width="viewport.isLessThan('tablet') ? '100%' : '31%'" />
					<n-skeleton height="200px" :width="viewport.isLessThan('tablet') ? '100%' : '31%'" />
				</n-flex>
			</n-flex>

			<n-space v-if="fetchStatus === 'pending'" vertical>
				<n-card class="h-64 mb-4">
					<template #header>
						<n-skeleton text width="60%" />
					</template>
					<n-skeleton text :repeat="6" />
				</n-card>

				<n-h1 prefix="bar">
					<h1>
						DASHBOARD
						<n-button circle type="secondary" :loading="reloadLoading">
							<template #icon>
								<Icon :name="ReloadIcon" />
							</template>
						</n-button>
					</h1>
				</n-h1>

				<n-row :gutter="[8, 8]">
					<n-col :span="viewport.isLessThan('desktop') ? 24 : 8">
						<n-card>
							<template #header>
								<n-skeleton text width="60%" />
							</template>
							<n-skeleton text :repeat="6" />
						</n-card>
					</n-col>
					<n-col :span="viewport.isLessThan('desktop') ? 24 : 8">
						<n-card>
							<template #header>
								<n-skeleton text width="60%" />
							</template>
							<n-skeleton text :repeat="6" />
						</n-card>
					</n-col>
					<n-col :span="viewport.isLessThan('desktop') ? 24 : 8">
						<n-card>
							<template #header>
								<n-skeleton text width="60%" />
							</template>
							<n-skeleton text :repeat="6" />
						</n-card>
					</n-col>
				</n-row>
			</n-space>

			<n-space v-if="fetchStatus === 'success'" vertical>
				<div
					class="relative w-full h-64 mb-4 bg-cover bg-center rounded-lg overflow-hidden"
					:style="{ backgroundImage: `url(${urlBackground})` }"
				>
					<div class="absolute inset-0 bg-primary opacity-40"></div>
					<div class="absolute inset-0 flex items-center justify-center">
						<h1 class="text-white text-3xl font-bold">{{ agendaData.title }}</h1>
					</div>
				</div>

				<n-h1 prefix="bar">
					<h1>
						DASHBOARD
						<n-button circle strong type="primary" ghost :loading="reloadLoading" @click="handleReload">
							<template #icon>
								<Icon :name="ReloadIcon" />
							</template>
						</n-button>
					</h1>
				</n-h1>

				<n-row :gutter="[8, 8]">
					<n-col :span="viewport.isLessThan('desktop') ? 24 : 8">
						<DashboardCardStat
							title="Undangan"
							valueText="hadir/absen"
							totalText="Total Undangan"
							:countPositive="aggregatedGuestEvent.invitation.attend.total"
							:countNegative="aggregatedGuestEvent.invitation.absent.total"
							:total="aggregatedGuestEvent.invitation.total"
							:icon="InvitationIcon"
							:detail="detailInvitation"
						/>
					</n-col>
					<n-col :span="viewport.isLessThan('desktop') ? 24 : 8">
						<DashboardCardStat
							title="Pax"
							valueText="hadir/absen"
							totalText="Total Pax"
							:countPositive="aggregatedGuestEvent.guest.attend.total"
							:countNegative="aggregatedGuestEvent.guest.absent.total"
							:total="aggregatedGuestEvent.guest.total"
							:icon="GuestIcon"
							:detail="detailAmountGuests"
						/>
					</n-col>
					<n-col :span="viewport.isLessThan('desktop') ? 24 : 8">
						<DashboardCardGift
							title="Hadiah"
							valueText="Total"
							:percentage="
								Number((aggregatedGuestEvent.gift.total / aggregatedGuestEvent.guest.total).toFixed(2))
							"
							:total="aggregatedGuestEvent.gift.total"
							:icon="GiftIcon"
							:detail="detailGift"
						/>
					</n-col>
				</n-row>
				<n-space v-if="isAdmin" class="mt-4" justify="end">
					<n-button type="primary" @click="showAnnouncementModal = true">
						<template #icon>
							<Icon :name="AddIcon" />
						</template>
						Pengumuman
					</n-button>
				</n-space>
				<n-alert
					v-for="alert in announcementData"
					:key="alert.id"
					:type="alert.type"
					:title="alert.title"
					:show-icon="alert.type != 'default'"
					:closable="isAdmin"
					@close="onAnnouncementClosed(alert.id)"
				>
					{{ alert.content }}
				</n-alert>
			</n-space>
		</template>
		<DashboardAnnouncementAdd
			v-model:show="showAnnouncementModal"
			:agenda="agendaData"
			@close="showAnnouncementModal = false"
			@after-submit="handleAfterSubmit"
			closable
		/>
	</div>
</template>

<script setup>
import {
	NAlert,
	NResult,
	NSpace,
	NH1,
	NCard,
	NRow,
	NCol,
	NFlex,
	NSkeleton,
	NButton,
	useMessage,
	useLoadingBar
} from "naive-ui"
import { useMainStore } from "@/stores/main"
import _includes from "lodash/includes"

definePageMeta({
	name: "Dashboard",
	title: "Dashboard"
})

const InvitationIcon = "octicon:person-24"
const GuestIcon = "octicon:people-24"
const GiftIcon = "carbon:gift"
const ReloadIcon = "mdi:reload"
const AddIcon = "la:plus"

const viewport = useViewport()
const mainStore = useMainStore()
const { getItemById, getItems, deleteItems } = useDirectusItems()
const route = useRoute()
const loadingBar = useLoadingBar()
const message = useMessage()
const user = useDirectusUser()
const reloadLoading = ref(false)

const isAdmin = ref(user.value.role.name === "Administrator")

const detailInvitation = ref([])
const detailAmountGuests = ref([])
const detailGift = ref([])

const haveAccess = ref(true)
const listAccess = ref([])
const agendaData = ref(null)
const fetchError = ref(null)
const fetchStatus = ref("idle")

let intervalId = null

const fetchAgenda = async () => {
	loadingBar.start()
	fetchStatus.value = "pending"
	try {
		const item = await getItemById({
			collection: "event",
			id: route.params.id,
			params: {
				fields: ["id", "title", "greeting_background", "users.*"]
			}
		})
		item.users.forEach(user => {
			listAccess.value.push(user.directus_users_id)
		})
		agendaData.value = item
		fetchStatus.value = "success"
		loadingBar.finish()
	} catch (e) {
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
		} else {
			console.error(e)
			message.error(e)
		}
		fetchStatus.value = "error"
		loadingBar.error()
	}
}

const { load: loadAggregate, aggregatedGuestEvent } = useFetchAggregateGuestEvent(route.params.id)
const fetchAggregate = async () => {
	try {
		await loadAggregate()

		detailInvitation.value = [
			{
				title: "Regular",
				total: aggregatedGuestEvent.invitation.regular,
				positive: aggregatedGuestEvent.invitation.attend.regular,
				negative: aggregatedGuestEvent.invitation.absent.regular
			},
			{
				title: "VIP",
				total: aggregatedGuestEvent.invitation.vip,
				positive: aggregatedGuestEvent.invitation.attend.vip,
				negative: aggregatedGuestEvent.invitation.absent.vip
			},
			{
				title: "VVIP",
				total: aggregatedGuestEvent.invitation.vvip,
				positive: aggregatedGuestEvent.invitation.attend.vvip,
				negative: aggregatedGuestEvent.invitation.absent.vvip
			}
		]

		detailAmountGuests.value = [
			{
				title: "Regular",
				total: aggregatedGuestEvent.guest.regular,
				positive: aggregatedGuestEvent.guest.attend.regular,
				negative: aggregatedGuestEvent.guest.absent.regular
			},
			{
				title: "VIP",
				total: aggregatedGuestEvent.guest.vip,
				positive: aggregatedGuestEvent.guest.attend.vip,
				negative: aggregatedGuestEvent.guest.absent.vip
			},
			{
				title: "VVIP",
				total: aggregatedGuestEvent.guest.vvip,
				positive: aggregatedGuestEvent.guest.attend.vvip,
				negative: aggregatedGuestEvent.guest.absent.vvip
			}
		]

		detailGift.value = [
			{ title: "Amplop", total: aggregatedGuestEvent.gift.cash },
			{ title: "Hadiah Fisik", total: aggregatedGuestEvent.gift.present },
			{ title: "Transfer", total: aggregatedGuestEvent.gift.transfer }
		]
	} catch (e) {
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
		} else {
			console.error(e)
			message.error(e)
		}
	}
}

const announcementData = ref(null)
const showAnnouncementModal = ref(false)
const fetchAnnouncement = async () => {
	try {
		const item = await getItems({
			collection: "announcement",
			params: {
				filter: {
					event: {
						_eq: route.params.id
					}
				}
			}
		})
		announcementData.value = item
	} catch (e) {
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
		} else {
			console.error(e)
			message.error(e)
		}
	}
}

const handleAfterSubmit = async () => {
	await fetchAnnouncement()
	showAnnouncementModal.value = false
}

const onAnnouncementClosed = async AnnouncementId => {
	try {
		await deleteItems({
			collection: "announcement",
			items: [AnnouncementId]
		})
		message.success(`Pengumuman dihapus`)
	} catch (e) {
		console.error(e)
		message.error("Error deleting event")
	}
}

const refresh = e => {
	mainStore.softReload()
	return e
}

const urlBackground = computed(
	() =>
		agendaData.value?.greeting_background ||
		"https://directus.bagimomen.my.id/assets/2f648530-7a19-4f74-adb1-bebd1daef911/default_bg.jpg"
)

const handleReload = async () => {
	reloadLoading.value = true
	await fetchAgenda()
	await fetchAggregate()
	await fetchAnnouncement()
	reloadLoading.value = false
}

onMounted(async () => {
	await fetchAgenda()
	await fetchAggregate()
	if (!isAdmin.value) {
		haveAccess.value = _includes(listAccess.value, user.value.id) ? true : false
	}
	await fetchAnnouncement()

	// Set up an interval to fetch data every 5 seconds
	/* intervalId = setInterval(async () => {
		await fetchAggregate()
	}, 5000) */
})

onBeforeUnmount(() => {
	// Clear the interval when the component is destroyed
	// clearInterval(intervalId)
})
</script>

<style lang="scss" scoped></style>
