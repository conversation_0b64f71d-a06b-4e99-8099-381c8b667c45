{"version": 3, "file": "webbluetooth-receipt-printer.esm.js", "sources": ["../src/event-emitter.js", "../src/callback-queue.js", "../src/main.js"], "sourcesContent": ["class EventEmitter {\n    constructor(device) {\n        this._events = {};\n    }\n\n    on(e, f) {\n        this._events[e] = this._events[e] || [];\n        this._events[e].push(f);\n    }\n\n    emit(e, ...args) {\n        let fs = this._events[e];\n        if (fs) {\n            fs.forEach(f => {\n                setTimeout(() => f(...args), 0);\n            });\n        }\n    }        \n}\n\nexport default EventEmitter;", "class CallbackQueue {\n    constructor() {\n        this._queue = [];\n        this._working = false;\n    }\n\n    add(data) {\n        let that = this;\n\n        async function run() {\n            if (!that._queue.length) {\n                that._working = false;\n                return;\n            }\n\n            that._working = true;\n\n            let callback = that._queue.shift();\n            await callback();\n\n            run();\n        }\n\n        this._queue.push(data);\n\n        if (!this._working) {\n            run();\n        }\n    }\n\n    sleep(ms) {\n        this.add(() => new Promise(resolve => setTimeout(resolve, ms)));\n    }\n}\n\nexport default CallbackQueue;", "import EventEmitter from \"./event-emitter.js\";\nimport CallbackQueue from \"./callback-queue.js\";\n\nconst DeviceProfiles = [\n\n\t/* Epson TM-P series, for example the TM-P20II */\n\t{\n\t\tfilters: [\n\t\t\t{\n\t\t\t\tnamePrefix: 'TM-P'\n\t\t\t}\n\t\t],\n\n\t\tfunctions: {\n\t\t\t'print':\t\t{\n\t\t\t\tservice: \t\t'49535343-fe7d-4ae5-8fa9-9fafd205e455',\n\t\t\t\tcharacteristic:\t'49535343-8841-43f4-a8d4-ecbe34729bb3'\n\t\t\t},\n\n\t\t\t'status':\t\t{\n\t\t\t\tservice: \t\t'49535343-fe7d-4ae5-8fa9-9fafd205e455',\n\t\t\t\tcharacteristic:\t'49535343-1e4d-4bd9-ba61-23c647249616'\n\t\t\t}\n\t\t},\n\n\t\tlanguage:\t\t\t'esc-pos',\n\t\tcodepageMapping:\t'epson'\n\t},\n\n\t/* Star SM-L series, for example the SM-L200 */\n\t{\n\t\tfilters: [\n\t\t\t{\n\t\t\t\tnamePrefix: 'STAR L'\n\t\t\t}\n\t\t],\n\n\t\tfunctions: {\n\t\t\t'print':\t\t{\n\t\t\t\tservice: \t\t'49535343-fe7d-4ae5-8fa9-9fafd205e455',\n\t\t\t\tcharacteristic:\t'49535343-8841-43f4-a8d4-ecbe34729bb3'\n\t\t\t},\n\n\t\t\t'status':\t\t{\n\t\t\t\tservice: \t\t'49535343-fe7d-4ae5-8fa9-9fafd205e455',\n\t\t\t\tcharacteristic:\t'49535343-1e4d-4bd9-ba61-23c647249616'\n\t\t\t}\n\t\t},\n\n\t\tlanguage:\t\t\t'star-line',\n\t\tcodepageMapping:\t'star'\n\t},\n\n\t/* POS-5805, POS-8360 and similar printers */\n\t{\n\t\tfilters: [ \n\t\t\t{ \n\t\t\t\tname: \t\t'BlueTooth Printer',\n\t\t\t\tservices: \t[ '000018f0-0000-1000-8000-00805f9b34fb' ] \n\t\t\t}\n\t\t],\n\t\t\n\t\tfunctions: {\n\t\t\t'print':\t\t{\n\t\t\t\tservice: \t\t'000018f0-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'00002af1-0000-1000-8000-00805f9b34fb'\n\t\t\t},\n\n\t\t\t'status':\t\t{\n\t\t\t\tservice: \t\t'000018f0-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'00002af0-0000-1000-8000-00805f9b34fb'\n\t\t\t}\n\t\t},\n\n\t\tlanguage:\t\t\t'esc-pos',\n\t\tcodepageMapping:\t'zjiang'\n\t}, \n\n\t/* Xprinter */\n\t{\n\t\tfilters: [ \n\t\t\t{ \n\t\t\t\tname: \t\t'Printer001',\n\t\t\t\tservices: \t[ '000018f0-0000-1000-8000-00805f9b34fb' ] \n\t\t\t} \n\t\t],\n\t\t\n\t\tfunctions: {\n\t\t\t'print':\t\t{\n\t\t\t\tservice: \t\t'000018f0-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'00002af1-0000-1000-8000-00805f9b34fb'\n\t\t\t},\n\n\t\t\t'status':\t\t{\n\t\t\t\tservice: \t\t'000018f0-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'00002af0-0000-1000-8000-00805f9b34fb'\n\t\t\t}\n\t\t},\n\n\t\tlanguage:\t\t\t'esc-pos',\n\t\tcodepageMapping:\t'xprinter'\n\t}, \n\n\t/* MPT-II printer */\n\t{\n\t\tfilters: [ \n\t\t\t{ \n\t\t\t\tname: \t\t'MPT-II',\n\t\t\t\tservices: \t[ '000018f0-0000-1000-8000-00805f9b34fb' ] \n\t\t\t} \n\t\t],\n\t\t\n\t\tfunctions: {\n\t\t\t'print':\t\t{\n\t\t\t\tservice: \t\t'000018f0-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'00002af1-0000-1000-8000-00805f9b34fb'\n\t\t\t},\n\n\t\t\t'status':\t\t{\n\t\t\t\tservice: \t\t'000018f0-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'00002af0-0000-1000-8000-00805f9b34fb'\n\t\t\t}\n\t\t},\n\n\t\tlanguage:\t\t\t'esc-pos',\n\t\tcodepageMapping:\t'mpt'\n\t},\n\n\t/* Cat printer */\n\t{\n\t\tfilters: [ \n\t\t\t{ \n\t\t\t\tservices: \t[ '0000ae30-0000-1000-8000-00805f9b34fb' ] \n\t\t\t} \n\t\t],\n\t\t\n\t\tfunctions: {\n\t\t\t'print':\t\t{\n\t\t\t\tservice: \t\t'0000ae30-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'0000ae01-0000-1000-8000-00805f9b34fb'\n\t\t\t},\n\n\t\t\t'notify':\t\t{\n\t\t\t\tservice: \t\t'0000ae30-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'0000ae02-0000-1000-8000-00805f9b34fb'\n\t\t\t}\n\n\t\t},\n\n\t\tlanguage:\t\t\t'meow',\n\t\tcodepageMapping:\t'default',\n\t\tmessageSize:\t\t200,\n\t\tsleepAfterCommand:\t30\n\t},\n\n\t/* Generic printer */\n\t{\n\t\tfilters: [ \n\t\t\t{ \n\t\t\t\tservices: \t[ '000018f0-0000-1000-8000-00805f9b34fb' ] \n\t\t\t} \n\t\t],\n\t\t\n\t\tfunctions: {\n\t\t\t'print':\t\t{\n\t\t\t\tservice: \t\t'000018f0-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'00002af1-0000-1000-8000-00805f9b34fb'\n\t\t\t},\n\n\t\t\t'status':\t\t{\n\t\t\t\tservice: \t\t'000018f0-0000-1000-8000-00805f9b34fb',\n\t\t\t\tcharacteristic:\t'00002af0-0000-1000-8000-00805f9b34fb'\n\t\t\t}\n\t\t},\n\n\t\tlanguage:\t\t\t'esc-pos',\n\t\tcodepageMapping:\t'default'\n\t}\n]\n\nclass ReceiptPrinterDriver {}\n\nclass WebBluetoothReceiptPrinter extends ReceiptPrinterDriver {\n\n\t#emitter;\n\t#queue;\n\t\n\t#device = null;\n\t#profile = null;\n\t#characteristics = {\n\t\tprint: \tnull,\n\t\tstatus: null\n\t};\n\n\tconstructor() {\n\t\tsuper();\n\n\t\tthis.#emitter = new EventEmitter();\n\t\tthis.#queue = new CallbackQueue();\n\n\t\tnavigator.bluetooth.addEventListener('disconnect', event => {\n\t\t\tif (this.#device == event.device) {\n\t\t\t\tthis.#emitter.emit('disconnected');\n\t\t\t}\n\t\t});\n\t}\n\n\tasync connect() {\n\t\tlet filters = DeviceProfiles.map(i => i.filters).reduce((a, b) => a.concat(b));\n\t\tlet optionalServices = DeviceProfiles.map(i => Object.values(i.functions).map(f => f.service)).reduce((a, b) => a.concat(b)).filter((v, i, a) => a.indexOf(v) === i);\n\n\t\ttry {\n\t\t\tlet device = await navigator.bluetooth.requestDevice({ \n\t\t\t\tfilters, optionalServices\n\t\t\t});\n\t\t\t\n\t\t\tif (device) {\n\t\t\t\tawait this.#open(device);\n\t\t\t}\n\t\t}\n\t\tcatch(error) {\n\t\t\tconsole.log('Could not connect! ' + error);\n\t\t}\n\t}\n\n\tasync reconnect(previousDevice) {\n\t\tif (!navigator.bluetooth.getDevices) {\n\t\t\treturn;\n\t\t}\n\n\t\tlet devices = await navigator.bluetooth.getDevices();\n\n\t\tlet device = devices.find(device => device.id == previousDevice.id);\n\n\t\tif (device) {\n\t\t\tawait this.#open(device);\n\t\t}\n\t}\n\n\tasync #open(device) {\n\t\tthis.#device = device;\n\n\t\tlet server = await this.#device.gatt.connect();\n\t\tlet services = await server.getPrimaryServices();\n\t\tlet uuids = services.map(service => service.uuid);\n\n\t\t/* Find profile for device */\n\n\t\tthis.#profile = DeviceProfiles.find(item => item.filters.some(filter => this.#evaluateFilter(filter, uuids)));\n\n\t\t/* Get characteristics and service for printing */\n\t\t\n\t\tlet printService = await server.getPrimaryService(this.#profile.functions.print.service);\n\t\t\n\t\tthis.#characteristics.print = \n\t\t\tawait printService.getCharacteristic(this.#profile.functions.print.characteristic);\n\t\t\n\t\t/* Get characteristics and service for status */\n\t\t\n\t\tif (this.#profile.functions.status) \n\t\t{\n\t\t\tlet statusService = await server.getPrimaryService(this.#profile.functions.status.service);\n\n\t\t\tthis.#characteristics.status = \n\t\t\t\tawait statusService.getCharacteristic(this.#profile.functions.status.characteristic);\n\t\t}\n\n\t\t/* Emit connected event */\n\n\t\tthis.#emitter.emit('connected', {\n\t\t\ttype:\t\t\t\t'bluetooth',\n\t\t\tname: \t\t\t\tthis.#device.name,\n\t\t\tid: \t\t\t\tthis.#device.id,\n\t\t\tlanguage: \t\t\tawait this.#evaluate(this.#profile.language),\n\t\t\tcodepageMapping:\tawait this.#evaluate(this.#profile.codepageMapping)\n\t\t});\n\t}\n\n\tasync #evaluate(expression) {\n\t\tif (typeof expression == 'function') {\n\t\t\treturn await expression(this.#device);\n\t\t}\n\n\t\treturn expression;\n\t}\n\n\t#evaluateFilter(filter, uuids) {\n\t\tif (filter.services) {\n\t\t\tfor (let service of filter.services) {\n\t\t\t\tif (!uuids.includes(service)) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (filter.name) {\n\t\t\tif (this.#device.name != filter.name) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tif (filter.namePrefix) {\n\t\t\tif (!this.#device.name.startsWith(filter.namePrefix)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\t\n\tasync listen() {\n\t\tif (this.#characteristics.status) {\t\n\t\t\tawait this.#characteristics.status.startNotifications();\n\n\t\t\tthis.#characteristics.status.addEventListener( \"characteristicvaluechanged\", (e) => {\n\t\t\t\tthis.#emitter.emit('data', e.target.value);\n\t\t\t});\n\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tasync disconnect() {\n\t\tif (!this.#device) {\n\t\t\treturn;\n\t\t}\n\n\t\tawait this.#device.gatt.disconnect();\n\n\t\tthis.#device = null;\n\t\tthis.#characteristics.print = null;\n\t\tthis.#characteristics.status = null;\n\t\tthis.#profile = null;\n\n\t\tthis.#emitter.emit('disconnected');\n\t}\n\t\n\tprint(commands) {\n\t\treturn new Promise(resolve => {\n\t\t\tif (ArrayBuffer.isView(commands)) {\n\t\t\t\tcommands = [ commands ];\n\t\t\t}\n\t\t\t\n\t\t\tfor (let command of commands) {\n\t\t\t\tconst maxLength = this.#profile.messageSize || 100;\n\t\t\t\tlet chunks = Math.ceil(command.length / maxLength);\n\t\t\n\t\t\t\tif (chunks === 1) {\n\t\t\t\t\tlet data = command;\n\n\t\t\t\t\tthis.#queue.add(() => this.#characteristics.print.writeValueWithResponse(data));\n\n\t\t\t\t\tif (this.#profile.sleepAfterCommand) {\n\t\t\t\t\t\tthis.#queue.sleep(this.#profile.sleepAfterCommand);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tfor (let i = 0; i < chunks; i++) {\n\t\t\t\t\t\tlet byteOffset = i * maxLength;\n\t\t\t\t\t\tlet length = Math.min(command.length, byteOffset + maxLength);\n\t\t\t\t\t\tlet data = command.slice(byteOffset, length);\n\n\t\t\t\t\t\tthis.#queue.add(() => this.#characteristics.print.writeValueWithResponse(data));\n\n\t\t\t\t\t\tif (this.#profile.sleepAfterCommand) {\n\t\t\t\t\t\t\tthis.#queue.sleep(this.#profile.sleepAfterCommand);\n\t\t\t\t\t\t}\t\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\tthis.#queue.add(() => resolve());\n\t\t});\n\t}\n\n\taddEventListener(n, f) {\n\t\tthis.#emitter.on(n, f);\n\t}\n}\n\nexport default WebBluetoothReceiptPrinter;"], "names": ["EventEmitter", "constructor", "device", "this", "_events", "on", "e", "f", "push", "emit", "args", "fs", "for<PERSON>ach", "setTimeout", "Callback<PERSON><PERSON><PERSON>", "_queue", "_working", "add", "data", "that", "async", "run", "length", "callback", "shift", "sleep", "ms", "Promise", "resolve", "DeviceProfiles", "filters", "namePrefix", "functions", "print", "service", "characteristic", "status", "language", "codepageMapping", "name", "services", "notify", "messageSize", "sleepAfterCommand", "ReceiptPrinterDriver", "WebBluetoothReceiptPrinter", "emitter", "queue", "profile", "characteristics", "super", "navigator", "bluetooth", "addEventListener", "event", "map", "i", "reduce", "a", "b", "concat", "optionalServices", "Object", "values", "filter", "v", "indexOf", "requestDevice", "open", "error", "console", "log", "previousDevice", "getDevices", "find", "id", "server", "gatt", "connect", "uuids", "getPrimaryServices", "uuid", "item", "some", "evaluateFilter", "printService", "getPrimaryService", "getCharacteristic", "statusService", "type", "evaluate", "expression", "includes", "startsWith", "startNotifications", "target", "value", "disconnect", "commands", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "command", "max<PERSON><PERSON><PERSON>", "chunks", "Math", "ceil", "writeValueWithResponse", "byteOffset", "min", "slice", "n"], "mappings": "AAAA,MAAMA,EACFC,YAAYC,GACRC,KAAKC,QAAU,EAClB,CAEDC,GAAGC,EAAGC,GACFJ,KAAKC,QAAQE,GAAKH,KAAKC,QAAQE,IAAM,GACrCH,KAAKC,QAAQE,GAAGE,KAAKD,EACxB,CAEDE,KAAKH,KAAMI,GACP,IAAIC,EAAKR,KAAKC,QAAQE,GAClBK,GACAA,EAAGC,SAAQL,IACPM,YAAW,IAAMN,KAAKG,IAAO,EAAE,GAG1C,ECjBL,MAAMI,EACFb,cACIE,KAAKY,OAAS,GACdZ,KAAKa,UAAW,CACnB,CAEDC,IAAIC,GACA,IAAIC,EAAOhB,KAgBXA,KAAKY,OAAOP,KAAKU,GAEZf,KAAKa,UAhBVI,eAAeC,IACX,IAAKF,EAAKJ,OAAOO,OAEb,YADAH,EAAKH,UAAW,GAIpBG,EAAKH,UAAW,EAEhB,IAAIO,EAAWJ,EAAKJ,OAAOS,cACrBD,IAENF,GACH,CAKGA,EAEP,CAEDI,MAAMC,GACFvB,KAAKc,KAAI,IAAM,IAAIU,SAAQC,GAAWf,WAAWe,EAASF,MAC7D,EC7BL,MAAMG,EAAiB,CAGtB,CACCC,QAAS,CACR,CACCC,WAAY,SAIdC,UAAW,CACVC,MAAU,CACTC,QAAW,uCACXC,eAAgB,wCAGjBC,OAAW,CACVF,QAAW,uCACXC,eAAgB,yCAIlBE,SAAY,UACZC,gBAAiB,SAIlB,CACCR,QAAS,CACR,CACCC,WAAY,WAIdC,UAAW,CACVC,MAAU,CACTC,QAAW,uCACXC,eAAgB,wCAGjBC,OAAW,CACVF,QAAW,uCACXC,eAAgB,yCAIlBE,SAAY,YACZC,gBAAiB,QAIlB,CACCR,QAAS,CACR,CACCS,KAAQ,oBACRC,SAAW,CAAE,0CAIfR,UAAW,CACVC,MAAU,CACTC,QAAW,uCACXC,eAAgB,wCAGjBC,OAAW,CACVF,QAAW,uCACXC,eAAgB,yCAIlBE,SAAY,UACZC,gBAAiB,UAIlB,CACCR,QAAS,CACR,CACCS,KAAQ,aACRC,SAAW,CAAE,0CAIfR,UAAW,CACVC,MAAU,CACTC,QAAW,uCACXC,eAAgB,wCAGjBC,OAAW,CACVF,QAAW,uCACXC,eAAgB,yCAIlBE,SAAY,UACZC,gBAAiB,YAIlB,CACCR,QAAS,CACR,CACCS,KAAQ,SACRC,SAAW,CAAE,0CAIfR,UAAW,CACVC,MAAU,CACTC,QAAW,uCACXC,eAAgB,wCAGjBC,OAAW,CACVF,QAAW,uCACXC,eAAgB,yCAIlBE,SAAY,UACZC,gBAAiB,OAIlB,CACCR,QAAS,CACR,CACCU,SAAW,CAAE,0CAIfR,UAAW,CACVC,MAAU,CACTC,QAAW,uCACXC,eAAgB,wCAGjBM,OAAW,CACVP,QAAW,uCACXC,eAAgB,yCAKlBE,SAAY,OACZC,gBAAiB,UACjBI,YAAc,IACdC,kBAAmB,IAIpB,CACCb,QAAS,CACR,CACCU,SAAW,CAAE,0CAIfR,UAAW,CACVC,MAAU,CACTC,QAAW,uCACXC,eAAgB,wCAGjBC,OAAW,CACVF,QAAW,uCACXC,eAAgB,yCAIlBE,SAAY,UACZC,gBAAiB,YAInB,MAAMM,GAEN,MAAMC,UAAmCD,EAExCE,GACAC,GAEA7C,GAAU,KACV8C,GAAW,KACXC,GAAmB,CAClBhB,MAAQ,KACRG,OAAQ,MAGTnC,cACCiD,QAEA/C,MAAK2C,EAAW,IAAI9C,EACpBG,MAAK4C,EAAS,IAAIjC,EAElBqC,UAAUC,UAAUC,iBAAiB,cAAcC,IAC9CnD,MAAKD,GAAWoD,EAAMpD,QACzBC,MAAK2C,EAASrC,KAAK,eACnB,GAEF,CAEDW,gBACC,IAAIU,EAAUD,EAAe0B,KAAIC,GAAKA,EAAE1B,UAAS2B,QAAO,CAACC,EAAGC,IAAMD,EAAEE,OAAOD,KACvEE,EAAmBhC,EAAe0B,KAAIC,GAAKM,OAAOC,OAAOP,EAAExB,WAAWuB,KAAIhD,GAAKA,EAAE2B,YAAUuB,QAAO,CAACC,EAAGC,IAAMD,EAAEE,OAAOD,KAAIK,QAAO,CAACC,EAAGT,EAAGE,IAAMA,EAAEQ,QAAQD,KAAOT,IAElK,IACC,IAAItD,QAAeiD,UAAUC,UAAUe,cAAc,CACpDrC,UAAS+B,qBAGN3D,SACGC,MAAKiE,EAAMlE,EAElB,CACD,MAAMmE,GACLC,QAAQC,IAAI,sBAAwBF,EACpC,CACD,CAEDjD,gBAAgBoD,GACf,IAAKrB,UAAUC,UAAUqB,WACxB,OAGD,IAEIvE,SAFgBiD,UAAUC,UAAUqB,cAEnBC,MAAKxE,GAAUA,EAAOyE,IAAMH,EAAeG,KAE5DzE,SACGC,MAAKiE,EAAMlE,EAElB,CAEDkB,QAAYlB,GACXC,MAAKD,EAAUA,EAEf,IAAI0E,QAAezE,MAAKD,EAAQ2E,KAAKC,UAEjCC,SADiBH,EAAOI,sBACPzB,KAAIrB,GAAWA,EAAQ+C,OAI5C9E,MAAK6C,EAAWnB,EAAe6C,MAAKQ,GAAQA,EAAKpD,QAAQqD,MAAKnB,GAAU7D,MAAKiF,EAAgBpB,EAAQe,OAIrG,IAAIM,QAAqBT,EAAOU,kBAAkBnF,MAAK6C,EAAShB,UAAUC,MAAMC,SAOhF,GALA/B,MAAK8C,EAAiBhB,YACfoD,EAAaE,kBAAkBpF,MAAK6C,EAAShB,UAAUC,MAAME,gBAIhEhC,MAAK6C,EAAShB,UAAUI,OAC5B,CACC,IAAIoD,QAAsBZ,EAAOU,kBAAkBnF,MAAK6C,EAAShB,UAAUI,OAAOF,SAElF/B,MAAK8C,EAAiBb,aACfoD,EAAcD,kBAAkBpF,MAAK6C,EAAShB,UAAUI,OAAOD,eACtE,CAIDhC,MAAK2C,EAASrC,KAAK,YAAa,CAC/BgF,KAAS,YACTlD,KAAUpC,MAAKD,EAAQqC,KACvBoC,GAAQxE,MAAKD,EAAQyE,GACrBtC,eAAmBlC,MAAKuF,EAAUvF,MAAK6C,EAASX,UAChDC,sBAAuBnC,MAAKuF,EAAUvF,MAAK6C,EAASV,kBAErD,CAEDlB,QAAgBuE,GACf,MAAyB,mBAAdA,QACGA,EAAWxF,MAAKD,GAGvByF,CACP,CAEDP,GAAgBpB,EAAQe,GACvB,GAAIf,EAAOxB,SACV,IAAK,IAAIN,KAAW8B,EAAOxB,SAC1B,IAAKuC,EAAMa,SAAS1D,GACnB,OAAO,EAKV,QAAI8B,EAAOzB,MACNpC,MAAKD,EAAQqC,MAAQyB,EAAOzB,SAK7ByB,EAAOjC,aACL5B,MAAKD,EAAQqC,KAAKsD,WAAW7B,EAAOjC,YAM1C,CAEDX,eACC,QAAIjB,MAAK8C,EAAiBb,eACnBjC,MAAK8C,EAAiBb,OAAO0D,qBAEnC3F,MAAK8C,EAAiBb,OAAOiB,iBAAkB,8BAA+B/C,IAC7EH,MAAK2C,EAASrC,KAAK,OAAQH,EAAEyF,OAAOC,MAAM,KAGpC,EAIR,CAED5E,mBACMjB,MAAKD,UAIJC,MAAKD,EAAQ2E,KAAKoB,aAExB9F,MAAKD,EAAU,KACfC,MAAK8C,EAAiBhB,MAAQ,KAC9B9B,MAAK8C,EAAiBb,OAAS,KAC/BjC,MAAK6C,EAAW,KAEhB7C,MAAK2C,EAASrC,KAAK,gBACnB,CAEDwB,MAAMiE,GACL,OAAO,IAAIvE,SAAQC,IACduE,YAAYC,OAAOF,KACtBA,EAAW,CAAEA,IAGd,IAAK,IAAIG,KAAWH,EAAU,CAC7B,MAAMI,EAAYnG,MAAK6C,EAASN,aAAe,IAC/C,IAAI6D,EAASC,KAAKC,KAAKJ,EAAQ/E,OAASgF,GAExC,GAAe,IAAXC,EAAc,CACjB,IAAIrF,EAAOmF,EAEXlG,MAAK4C,EAAO9B,KAAI,IAAMd,MAAK8C,EAAiBhB,MAAMyE,uBAAuBxF,KAErEf,MAAK6C,EAASL,mBACjBxC,MAAK4C,EAAOtB,MAAMtB,MAAK6C,EAASL,kBAEtC,MACK,IAAK,IAAIa,EAAI,EAAGA,EAAI+C,EAAQ/C,IAAK,CAChC,IAAImD,EAAanD,EAAI8C,EACjBhF,EAASkF,KAAKI,IAAIP,EAAQ/E,OAAQqF,EAAaL,GAC/CpF,EAAOmF,EAAQQ,MAAMF,EAAYrF,GAErCnB,MAAK4C,EAAO9B,KAAI,IAAMd,MAAK8C,EAAiBhB,MAAMyE,uBAAuBxF,KAErEf,MAAK6C,EAASL,mBACjBxC,MAAK4C,EAAOtB,MAAMtB,MAAK6C,EAASL,kBAEjC,CAEF,CAEDxC,MAAK4C,EAAO9B,KAAI,IAAMW,KAAU,GAEjC,CAEDyB,iBAAiByD,EAAGvG,GACnBJ,MAAK2C,EAASzC,GAAGyG,EAAGvG,EACpB"}