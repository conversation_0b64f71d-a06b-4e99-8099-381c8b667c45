<template>
	<CustomDrawer v-model:show="show" title="Tambah Pengumuman" @close="handleClose">
		<n-form ref="formAnnouncementRef" :rules="rules" :model="AnnouncementModel">
			<n-form-item label="Judul" path="title">
				<n-input v-model:value="AnnouncementModel.title" type="text" clearable placeholder="Masukkan Judul" />
			</n-form-item>
			<n-form-item label="Tipe" path="type">
				<n-select v-model:value="AnnouncementModel.type" :options="opstionsType" />
			</n-form-item>
			<n-form-item label="Isi" path="content">
				<n-input
					v-model:value="AnnouncementModel.content"
					type="textarea"
					clearable
					placeholder="Masukkan Isi"
				/>
			</n-form-item>
		</n-form>
		<template #footer>
			<n-button block :loading="submitLoading" type="primary" @click="handleSubmit">Simpan</n-button>
		</template>
	</CustomDrawer>
</template>

<script setup>
import { NSelect, NForm, NFormItem, NInput, NButton, useLoadingBar, useMessage } from "naive-ui"
import CustomDrawer from "@/components/app/CustomDrawer.vue"

const props = defineProps({
	agenda: Object
})

const emit = defineEmits(["close", "after-submit"])

const show = defineModel()

const loadingBar = useLoadingBar()
const message = useMessage()
const { createItems } = useDirectusItems()
const formAnnouncementRef = ref(null)

const AnnouncementModel = ref({
	title: null,
	type: "default",
	content: null
})

const resetAnnouncementModel = () => {
	AnnouncementModel.value = {
		title: null,
		type: "default",
		content: null
	}
}

const opstionsType = [
	{
		label: "normal",
		value: "default"
	},
	{
		label: "info",
		value: "info"
	},
	{
		label: "checklist",
		value: "success"
	},
	{
		label: "peringatan",
		value: "warning"
	},
	{
		label: "larangan",
		value: "error"
	}
]

const rules = {
	title: {
		required: true,
		message: "Judul tidak boleh kosong",
		trigger: ["input", "blur"]
	},
	type: {
		required: true,
		message: "Tipe tidak boleh kosong",
		trigger: ["input", "blur"]
	}
}

const submitLoading = ref(false)

const handleSubmit = e => {
	e.preventDefault()
	formAnnouncementRef.value?.validate(async errors => {
		if (!errors) {
			await submitData()
		} else {
			console.error(errors)
		}
	})
}

const submitData = async () => {
	submitLoading.value = true
	loadingBar.start()
	try {
		AnnouncementModel.value.event = props.agenda.id
		await createItems({ collection: "announcement", items: AnnouncementModel.value })
		message.success("data telah ditambahkan")
		emit("after-submit")
		resetAnnouncementModel()
		loadingBar.finish()
	} catch (e) {
		console.error(e)
		message.error("terjadi kesalahan")
		loadingBar.error()
	} finally {
		submitLoading.value = false
	}
}

const handleClose = () => {
	emit("close")
}
</script>

<style lang="scss" scoped>
::v-deep(.n-input__suffix) {
	gap: 8px;
}
</style>
