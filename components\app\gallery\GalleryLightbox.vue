<template>
	<n-modal
		v-model:show="isVisible"
		preset="card"
		:style="{ maxWidth: '90vw', width: 'auto' }"
		class="gallery-lightbox"
		:mask-closable="true"
		:close-on-esc="true"
	>
		<div class="lightbox-container">
			<!-- Image -->
			<div class="image-container">
				<n-image
					:src="currentImageUrl"
					:preview-disabled="true"
					object-fit="contain"
					class="lightbox-image"
					:style="{ maxHeight: '80vh' }"
				/>
			</div>

			<!-- Guest info -->
			<div class="image-info mt-4">
				<h3 class="text-lg font-medium">{{ currentItem?.name }}</h3>
				<div class="flex flex-wrap gap-2 mt-2">
					<n-tag v-if="currentItem?.status_relation" type="info" size="small">
						{{ currentItem.status_relation }}
					</n-tag>
					<n-tag v-if="currentItem?.code_guest" type="success" size="small">
						{{ currentItem.code_guest }}
					</n-tag>
					<n-tag v-if="formattedDate" type="warning" size="small">
						{{ formattedDate }}
					</n-tag>
				</div>
			</div>

			<!-- Navigation buttons -->
			<div class="navigation-buttons">
				<n-button v-if="hasPrevious" circle class="prev-button" @click="navigateToPrevious">
					<template #icon>
						<Icon name="carbon:chevron-left" />
					</template>
				</n-button>

				<n-button v-if="hasNext" circle class="next-button" @click="navigateToNext">
					<template #icon>
						<Icon name="carbon:chevron-right" />
					</template>
				</n-button>
			</div>
		</div>
	</n-modal>
</template>

<script setup>
import { ref, computed, watch } from "vue"
import { NModal, NImage, NButton, NTag } from "naive-ui"
import Icon from "~/components/common/Icon.vue"

const props = defineProps({
	show: {
		type: Boolean,
		default: false
	},
	items: {
		type: Array,
		default: () => []
	},
	currentItemId: {
		type: String,
		default: null
	}
})

const emit = defineEmits(["update:show", "update:current-item-id"])

const config = useRuntimeConfig()
const isVisible = ref(props.show)

// Watch for external show changes
watch(
	() => props.show,
	newVal => {
		isVisible.value = newVal
	}
)

// Watch for internal show changes
watch(isVisible, newVal => {
	emit("update:show", newVal)
})

// Find the current item index
const currentIndex = computed(() => {
	if (!props.currentItemId || !props.items.length) return -1
	return props.items.findIndex(item => item.id === props.currentItemId)
})

// Get the current item
const currentItem = computed(() => {
	if (currentIndex.value === -1) return null
	return props.items[currentIndex.value]
})

// Compute the current image URL
const currentImageUrl = computed(() => {
	if (!currentItem.value?.selfie?.id) return ""
	return `${config.public.directusUrl}/assets/${currentItem.value.selfie.id}/${currentItem.value.selfie.filename_download}`
})

// Format the attendance date
const formattedDate = computed(() => {
	if (!currentItem.value?.attendance_time) return ""

	try {
		const date = new Date(currentItem.value.attendance_time)
		return date.toLocaleDateString("id-ID", {
			day: "numeric",
			month: "short",
			year: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		})
	} catch (e) {
		return ""
	}
})

// Check if there's a previous item
const hasPrevious = computed(() => {
	return currentIndex.value > 0
})

// Check if there's a next item
const hasNext = computed(() => {
	return currentIndex.value < props.items.length - 1 && currentIndex.value !== -1
})

// Navigate to the previous item
const navigateToPrevious = () => {
	if (!hasPrevious.value) return
	const prevItem = props.items[currentIndex.value - 1]
	emit("update:current-item-id", prevItem.id)
}

// Navigate to the next item
const navigateToNext = () => {
	if (!hasNext.value) return
	const nextItem = props.items[currentIndex.value + 1]
	emit("update:current-item-id", nextItem.id)
}
</script>

<style scoped>
.lightbox-container {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.image-container {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
}

.navigation-buttons {
	position: absolute;
	top: 50%;
	left: 0;
	right: 0;
	transform: translateY(-50%);
	display: flex;
	justify-content: space-between;
	padding: 0 1rem;
	pointer-events: none;
}

.prev-button,
.next-button {
	pointer-events: auto;
	opacity: 0.7;
	transition: opacity 0.2s;
}

.prev-button:hover,
.next-button:hover {
	opacity: 1;
}
</style>
