import { type DirectusQueryParams, type DirectusItems } from "nuxt-directus/dist/runtime/types/index"

import { type Guest } from "~/types/globals"
const { getItems } = useDirectusItems()
export const useGetGuests = () => {
	const data = ref(null)
	const meta = ref(null)
	const loading = ref(false)

	const load = async (fetchParams: DirectusQueryParams) => {
		loading.value = true
		const items: DirectusItems = await getItems<Guest>({
			collection: "guest",
			params: {
				...fetchParams,
				meta: "*"
			}
		})
		data.value = items.data
		meta.value = items.meta
		loading.value = false
	}

	return { data, meta, load, loading }
}
