/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    NCard: typeof import('naive-ui')['NCard']
    NCol: typeof import('naive-ui')['NCol']
    NFlex: typeof import('naive-ui')['NFlex']
    NH1: typeof import('naive-ui')['NH1']
    NRow: typeof import('naive-ui')['NRow']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NText: typeof import('naive-ui')['NText']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
