<template>
	<div class="page">
		<n-result v-if="fetchError" status="error" title="Error" description="<PERSON><PERSON><PERSON><PERSON>">
			<template #footer>
				<n-button @click="refresh">Refresh</n-button>
			</template>
		</n-result>

		<n-flex vertical>
			<n-h1 prefix="bar">
				<h1>ACARA</h1>
			</n-h1>
			<n-space>
				<n-input
					v-model:value="fetchParam.search"
					placeholder="Cari Event"
					clearable
					@keyup.enter="fetchAgendas"
					@clear="handleClear"
					@input="delayedSearch"
				>
					<template #prefix>
						<Icon :name="SearchIcon" />
					</template>
				</n-input>
				<n-button v-if="isAdmin" type="primary" @click="showAddForm = true">
					<template #icon>
						<Icon :name="AddIcon" :size="20" />
					</template>
				</n-button>
				<EventAdd v-model:show="showAddForm" @close="showAddForm = false" @after-submit="handleAfterSubmit" />
			</n-space>

			<!-- skeleton -->
			<n-flex v-if="fetchStatus === 'idle'">
				<n-skeleton
					v-for="i in 10"
					:key="i + 'skeleton'"
					class="mb-4"
					height="40px"
					width="100%"
					:sharp="false"
				/>
			</n-flex>

			<!-- empty -->
			<n-empty v-if="agendas && agendas.length < 1" size="large" description="data kosong">
				<template #extra>
					<n-button size="small" @click="fetchAgendas">refresh</n-button>
				</template>
			</n-empty>

			<!-- data -->
			<n-flex v-if="agendas && agendas.length > 0" vertical>
				<n-flex>
					<EventCard
						v-for="agenda in agendas"
						:key="agenda.id"
						:agenda="agenda"
						@edit="editEvent(agenda)"
						@delete="deleteEvent(agenda)"
						@title-click="goto(agenda)"
					/>
				</n-flex>
				<n-pagination
					v-model:page="fetchParam.page"
					:page-count="totalPages"
					:page-slot="7"
					@update:page="fetchAgendas"
				/>
			</n-flex>
		</n-flex>
		<EventUpdate
			v-model:show="showUpdateForm"
			:agenda="selectedAgenda"
			@close="showUpdateForm = false"
			@after-submit="handleAfterUpdate"
		/>
	</div>
</template>

<script setup>
import {
	NResult,
	NButton,
	NInput,
	NH1,
	NSpace,
	NFlex,
	NSkeleton,
	NEmpty,
	NPagination,
	useLoadingBar,
	useDialog,
	useMessage
} from "naive-ui"
import { useMainStore } from "@/stores/main"
import _debounce from "lodash/debounce"

definePageMeta({
	name: "Events",
	title: "Events"
})

const AddIcon = "ion:add"
const SearchIcon = "ion:search"

const mainStore = useMainStore()
const message = useMessage()
const dialog = useDialog()
const loadingBar = useLoadingBar()
const { getItems, deleteItems } = useDirectusItems()
const user = useDirectusUser()
const isAdmin = ref(user.value.role.name === "Administrator")

const agendas = ref(null)
const selectedAgenda = ref(null)
const agendasMeta = ref(null)
const fetchError = ref(null)
const fetchStatus = ref("idle")
const fetchParam = ref({
	limit: 20,
	page: 1,
	search: "",
	meta: "*",
	sort: ["-date_created"],
	fields: ["*", "users.*", "users.directus_users_id.*"]
})

const showAddForm = ref(false)
const showUpdateForm = ref(false)

const fetchAgendas = async () => {
	fetchStatus.value = "pending"
	loadingBar.start()
	try {
		if (!isAdmin.value) {
			fetchParam.value.filter = {
				users: {
					directus_users_id: {
						_eq: user.value.id
						// $CURRENT_USER
					}
				}
			}
		}
		const items = await getItems({
			collection: "event",
			params: fetchParam.value
		})
		agendas.value = items.data
		agendasMeta.value = items.meta
		fetchStatus.value = "success"
		loadingBar.finish()
	} catch (e) {
		loadingBar.error()
		fetchStatus.value = "error"
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
			fetchStatus.value = errorMessage
		} else {
			console.error(e)
			message.error(e)
			fetchStatus.value = e
		}
	}
}

const editEvent = agenda => {
	selectedAgenda.value = agenda
	showUpdateForm.value = true
}

const handleClear = async () => {
	fetchParam.value.search = ""
	fetchParam.value.page = 1
	await fetchAgendas()
}

const delayedSearch = _debounce(async () => {
	fetchParam.value.page = 1
	await fetchAgendas()
}, 500)

const refresh = e => {
	mainStore.softReload()
	return e
}

const handleAfterSubmit = async () => {
	await fetchAgendas()
	showAddForm.value = false
}

const handleAfterUpdate = async () => {
	await fetchAgendas()
	showUpdateForm.value = false
}

const deleteAgenda = async agenda => {
	try {
		await deleteItems({
			collection: "event",
			items: [agenda.id]
		})
		message.success(`event ${agenda.title} dihapus`)
	} catch (e) {
		console.error(e)
		message.error("Error deleting event")
	}
}

const deleteEvent = agenda => {
	const d = dialog.warning({
		title: "Konfirmasi",
		content: "Yakin hapus event ini?",
		positiveText: "Hapus",
		negativeText: "Batalkan",
		onPositiveClick: async () => {
			d.loading = true
			await deleteAgenda(agenda)
			await fetchAgendas()
			d.loading = false
		}
	})
}

const totalPages = computed(() => {
	return Math.ceil(agendasMeta.value.filter_count / fetchParam.value.limit)
})

const goto = agenda => {
	navigateTo(`/events/${agenda.id}/dashboard`)
}

onMounted(async () => {
	await fetchAgendas()
})
</script>

<style></style>
