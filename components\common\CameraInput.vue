<template>
	<div>
		<!-- Button to open camera -->
		<slot name="trigger" :open-camera="openCamera">
			<n-button @click="openCamera" type="primary">
				<template #icon>
					<Icon name="carbon:camera" :size="20" />
				</template>
				{{ buttonText }}
			</n-button>
		</slot>

		<!-- Camera Modal/Drawer -->
		<component
			:is="viewport.isLessThan('tablet') ? NDrawer : NModal"
			v-model:show="showCamera"
			:class="viewport.isLessThan('tablet') ? 'rounded-none' : ''"
			:width="viewport.isLessThan('tablet') ? '100%' : ''"
			:placement="viewport.isLessThan('tablet') ? 'right' : ''"
			@after-leave="cleanupCamera"
		>
			<component
				:is="viewport.isLessThan('tablet') ? NDrawerContent : NCard"
				:title="viewport.isGreaterOrEquals('tablet') ? title : ''"
				:size="viewport.isGreaterOrEquals('tablet') ? 'medium' : ''"
				:closable="viewport.isGreaterOrEquals('tablet')"
				:style="{ maxWidth: viewport.isGreaterOrEquals('tablet') ? '600px' : '100%' }"
				@close="closeCamera"
			>
				<template v-if="viewport.isLessThan('tablet')" #header>
					<n-page-header @back="closeCamera">
						<template #title>
							{{ title }}
						</template>
					</n-page-header>
				</template>

				<!-- Camera Content -->
				<div class="camera-container">
					<!-- Error alerts -->
					<n-alert v-if="noFrontCamera" title="Error" type="error">
						You don't seem to have a front camera on your device
					</n-alert>
					<n-alert v-if="noRearCamera" title="Error" type="error">
						You don't seem to have a rear camera on your device
					</n-alert>
					<n-alert v-if="cameraError" title="Error" type="error">
						{{ cameraError }}
					</n-alert>

					<!-- Camera view -->
					<n-spin :show="loading">
						<div v-show="!capturedImage" class="camera-view-container">
							<video
								ref="videoElement"
								class="camera-view"
								autoplay
								playsinline
								:style="`max-height: ${maxHeight}; aspect-ratio: 9/16;`"
							></video>

							<!-- Camera controls -->
							<div class="camera-controls">
								<n-space vertical>
									<n-button class="m-2" type="primary" strong secondary @click="switchCamera">
										<Icon name="fluent:camera-switch-20-filled" :size="24" />
									</n-button>
								</n-space>
							</div>

							<!-- Capture button -->
							<div class="capture-button-container">
								<n-button
									v-if="!capturedImage"
									class="capture-button"
									type="primary"
									circle
									@click="capturePhoto"
								>
									<Icon name="carbon:camera" :size="24" />
								</n-button>
							</div>
						</div>

						<!-- Preview captured image -->
						<div v-if="capturedImage" class="preview-container">
							<img
								:src="capturedImage"
								class="preview-image"
								:style="`max-height: ${maxHeight}; aspect-ratio: 9/16;`"
							/>
							<div class="preview-controls">
								<n-space justify="center">
									<n-button @click="retakePhoto" secondary>Retake</n-button>
									<n-button @click="confirmPhoto" type="primary">Use Photo</n-button>
								</n-space>
							</div>
						</div>

						<!-- Hidden canvas for image processing -->
						<canvas ref="canvasElement" style="display: none"></canvas>
					</n-spin>
				</div>
			</component>
		</component>
	</div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from "vue"
import { NButton, NDrawer, NModal, NDrawerContent, NCard, NPageHeader, NAlert, NSpin, NSpace } from "naive-ui"

const props = defineProps({
	title: {
		type: String,
		default: "Camera"
	},
	buttonText: {
		type: String,
		default: "Take Photo"
	},
	maxHeight: {
		type: String,
		default: "90vh"
	}
})

const emit = defineEmits(["capture", "close"])
const viewport = useViewport()

// Camera state
const showCamera = ref(false)
const loading = ref(true)
const cameraError = ref(null)
const noFrontCamera = ref(false)
const noRearCamera = ref(false)
const facingMode = ref("environment") // 'environment' for rear camera, 'user' for front camera
const capturedImage = ref(null)

// DOM references
const videoElement = ref(null)
const canvasElement = ref(null)

// Stream reference
let mediaStream = null

// Open camera modal/drawer
const openCamera = () => {
	showCamera.value = true
	loading.value = true
	cameraError.value = null
	capturedImage.value = null

	// Start camera after a short delay to ensure the modal is open
	setTimeout(() => {
		startCamera()
	}, 300)
}

// Close camera modal/drawer
const closeCamera = () => {
	showCamera.value = false
}

// Clean up camera resources when modal/drawer is closed
const cleanupCamera = () => {
	stopCamera()
	capturedImage.value = null
}

// Start the camera
const startCamera = async () => {
	try {
		// Stop any existing stream
		stopCamera()

		// Get camera stream
		const constraints = {
			video: {
				facingMode: facingMode.value,
				aspectRatio: 9 / 16 // Portrait orientation (9:16)
			},
			audio: false
		}

		mediaStream = await navigator.mediaDevices.getUserMedia(constraints)

		// Connect stream to video element
		if (videoElement.value) {
			videoElement.value.srcObject = mediaStream
		}

		loading.value = false
	} catch (err) {
		handleCameraError(err)
	}
}

// Stop the camera
const stopCamera = () => {
	if (mediaStream) {
		mediaStream.getTracks().forEach(track => track.stop())
		mediaStream = null
	}

	if (videoElement.value) {
		videoElement.value.srcObject = null
	}
}

// Switch between front and rear cameras
const switchCamera = () => {
	facingMode.value = facingMode.value === "environment" ? "user" : "environment"
	startCamera()
}

// Capture a photo
const capturePhoto = () => {
	if (!videoElement.value || !canvasElement.value) return

	const video = videoElement.value
	const canvas = canvasElement.value

	// Set canvas dimensions to match video aspect ratio
	canvas.width = video.videoWidth
	canvas.height = video.videoHeight

	// Draw video frame to canvas
	const ctx = canvas.getContext("2d")
	ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

	// Convert canvas to data URL
	capturedImage.value = canvas.toDataURL("image/jpeg")
}

// Retake photo
const retakePhoto = () => {
	capturedImage.value = null
}

// Confirm and use the captured photo
const confirmPhoto = () => {
	emit("capture", capturedImage.value)
	closeCamera()
}

// Handle camera errors
const handleCameraError = err => {
	loading.value = false

	const triedFrontCamera = facingMode.value === "user"
	const triedRearCamera = facingMode.value === "environment"

	const cameraMissingError = err.name === "OverconstrainedError"

	if (triedRearCamera && cameraMissingError) {
		noRearCamera.value = true
		// Try switching to front camera
		facingMode.value = "user"
		startCamera()
		return
	}

	if (triedFrontCamera && cameraMissingError) {
		noFrontCamera.value = true
	}

	if (err.name === "NotAllowedError") {
		cameraError.value = "You need to grant camera access permission"
	} else if (err.name === "NotFoundError") {
		cameraError.value = "No camera found on this device"
	} else if (err.name === "NotSupportedError") {
		cameraError.value = "Secure context required (HTTPS, localhost)"
	} else if (err.name === "NotReadableError") {
		cameraError.value = "Is the camera already in use?"
	} else if (err.name === "OverconstrainedError") {
		cameraError.value = "Installed cameras are not suitable"
	} else if (err.name === "StreamApiNotSupportedError") {
		cameraError.value = "Stream API is not supported in this browser"
	} else if (err.name === "InsecureContextError") {
		cameraError.value =
			"Camera access is only permitted in secure context. Use HTTPS or localhost rather than HTTP."
	} else {
		cameraError.value = err.message || "Unknown camera error"
	}

	console.error("Camera error:", err)
}

// Clean up on component unmount
onBeforeUnmount(() => {
	stopCamera()
})
</script>

<style scoped>
.camera-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
}

.camera-view-container {
	position: relative;
	width: 100%;
	display: flex;
	justify-content: center;
}

.camera-view {
	width: 100%;
	object-fit: cover;
	background-color: #000;
}

.camera-controls {
	position: absolute;
	top: 10px;
	right: 10px;
	z-index: 10;
}

.capture-button-container {
	position: absolute;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 10;
}

.capture-button {
	width: 60px;
	height: 60px;
}

.preview-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.preview-image {
	width: 100%;
	object-fit: contain;
}

.preview-controls {
	margin-top: 16px;
	width: 100%;
}
</style>
