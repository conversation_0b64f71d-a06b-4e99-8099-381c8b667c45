<template>
	<n-table>
		<thead>
			<tr>
				<th><PERSON><PERSON></th>
				<th>Hadiah</th>
				<th>usher</th>
			</tr>
		</thead>
		<tbody>
			<tr v-for="guest of guests" v-wave :key="guest.id" @click="emitSelect(guest)">
				<td>
					<div class="product flex items-center">
						<div class="product-info">
							<div class="product-name">
								{{ guest.name }}
							</div>
							<div class="product-category">
								{{ guest.status_relation || "-" }}
							</div>
						</div>
					</div>
				</td>
				<td>
					<div class="price">
						{{ giftsToString(guest.gift) }}
					</div>
					<n-text depth="3" v-if="guest.entrust_by">
						<data class="flex items-center">dibawa oleh {{ guest.entrust_by }}</data>
					</n-text>
				</td>
				<td>
					<div class="stock">
						{{ usher(guest) }}
					</div>
				</td>
			</tr>
		</tbody>
	</n-table>
</template>

<script setup>
import { NTable, NText } from "naive-ui"

defineProps({
	guests: Array
})
const emits = defineEmits(["select"])
const emitSelect = guest => {
	emits("select", guest)
}

const usher = guest => {
	let usher = "-"
	if (guest.regist_by) {
		if (guest.regist_by.first_name) {
			usher = `${guest.regist_by.first_name}`
		}
		if (guest.regist_by.last_name) {
			usher += ` ${guest.regist_by.last_name}`
		}
	}
	return usher
}
</script>

<style scoped lang="scss">
.product {
	.product-image {
		.n-image {
			:deep(img) {
				border-radius: var(--border-radius-small);
			}
		}
	}

	.product-info {
		.product-name {
			font-weight: 500;
			font-size: 16px;
			line-height: 1.2;
		}
		.product-category {
			opacity: 0.6;
		}
	}
}

.price {
	white-space: nowrap;
}

.orders {
	text-align: right;
	.orders-value {
		white-space: nowrap;
	}
}
</style>
