<template>
	<div class="page">
		<n-h1 prefix="bar" class="mb-4">
			<h1>PANDUAN MANAJEMEN TAMU</h1>
		</n-h1>
		
		<n-card>
			<n-space vertical size="large">
				<div>
					<n-h3>Statistik RSVP</n-h3>
					<n-ul>
						<n-li>Melihat jumlah tamu yang berencana hadir (hijau)</n-li>
						<n-li>Melihat jumlah tamu yang tidak hadir (merah)</n-li>
						<n-li>Melihat jumlah tamu yang belum konfirmasi (abu-abu)</n-li>
					</n-ul>
				</div>

				<div>
					<n-h3>Pencarian dan Filter</n-h3>
					<n-ol>
						<n-li>Gunakan kotak pencarian untuk mencari tamu berdasarkan nama</n-li>
						<n-li>Klik tombol filter untuk menampilkan opsi filter lanjutan</n-li>
						<n-li><PERSON><PERSON><PERSON> kolom yang ingin ditampilkan di tabel</n-li>
					</n-ol>
				</div>

				<div>
					<n-h3>Mengelola Data Tamu</n-h3>
					<n-space vertical>
						<n-flex align="center">
							<Icon :size="16" name="la:plus" color="var(--primary-color)" />
							<span><strong>Tambah Tamu:</strong> Klik tombol "+" untuk menambah tamu baru</span>
						</n-flex>
						<n-flex align="center">
							<Icon :size="16" name="la:cloud-upload-alt" color="var(--primary-color)" />
							<span><strong>Import:</strong> Upload file Excel/CSV untuk import data tamu massal</span>
						</n-flex>
						<n-flex align="center">
							<Icon :size="16" name="la:cloud-download-alt" color="var(--primary-color)" />
							<span><strong>Export:</strong> Download data tamu dalam format Excel atau CSV</span>
						</n-flex>
						<n-flex align="center">
							<Icon :size="16" name="la:redo-alt" color="var(--primary-color)" />
							<span><strong>Refresh:</strong> Muat ulang data terbaru</span>
						</n-flex>
					</n-space>
				</div>

				<div>
					<n-h3>Aksi pada Tabel</n-h3>
					<n-ul>
						<n-li>Klik nama tamu untuk mengedit data</n-li>
						<n-li>Centang checkbox untuk memilih multiple tamu</n-li>
						<n-li>Gunakan tombol hapus untuk menghapus tamu yang dipilih</n-li>
						<n-li>Navigasi halaman menggunakan pagination di bawah tabel</n-li>
					</n-ul>
				</div>

				<div>
					<n-h3>Format Import Data</n-h3>
					<n-alert type="info">
						<n-ul>
							<n-li>File harus berformat .xlsx atau .csv</n-li>
							<n-li>Kolom yang tersedia: Nama, Status/Relasi, Level, Sesi, Meja, Kategori, Alamat, No HP, dll</n-li>
							<n-li>Kode tamu akan dibuat otomatis jika tidak diisi</n-li>
							<n-li>Pastikan format data sesuai dengan template yang disediakan</n-li>
						</n-ul>
					</n-alert>
				</div>

				<div>
					<n-h3>Tips Penggunaan</n-h3>
					<n-alert type="success">
						<n-ul>
							<n-li>Gunakan pencarian untuk menemukan tamu dengan cepat</n-li>
							<n-li>Export data secara berkala sebagai backup</n-li>
							<n-li>Gunakan filter untuk melihat data berdasarkan kategori tertentu</n-li>
							<n-li>Periksa statistik RSVP untuk memantau konfirmasi kehadiran</n-li>
						</n-ul>
					</n-alert>
				</div>
			</n-space>
		</n-card>
	</div>
</template>

<script setup>
import { NH1, NH3, NCard, NSpace, NOl, NLi, NUl, NAlert, NFlex } from "naive-ui"

definePageMeta({
	name: "GuestsGuide",
	title: "Panduan Tamu"
})
</script>

<style scoped></style>
