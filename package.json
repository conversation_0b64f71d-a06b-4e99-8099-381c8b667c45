{"name": "pinx-nuxt-bagimomen", "version": "1.9.0", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test:unit": "vitest run --environment jsdom", "test:e2e": "start-server-and-test preview http://localhost:3000/ 'cypress open --e2e'", "test:e2e:ci": "start-server-and-test preview http://localhost:3000/ 'cypress run --e2e'", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "tailwind-config-viewer": "tailwind-config-viewer -o", "design-tokens": "node scripts/tokens-tool.js", "libs-check": "taze", "libs-reload": "rm -rf node_modules package-lock.json && npm install", "prettify": "prettier --write ."}, "dependencies": {"@directus/sdk": "^16.1.2", "@fontsource-variable/eb-garamond": "^5.0.21", "@fontsource-variable/jost": "^5.0.20", "@fontsource/jetbrains-mono": "^5.0.21", "@fontsource/lexend": "^5.0.21", "@fontsource/public-sans": "^5.0.18", "@formester/universal-font-picker": "^0.0.9", "@formkit/auto-animate": "^0.8.2", "@point-of-sale/receipt-printer-encoder": "^3.0.2", "@popperjs/core": "^2.11.8", "@vueuse/components": "^11.0.3", "@vueuse/core": "^11.0.3", "@vueuse/nuxt": "^11.0.3", "ably": "^2.3.1", "colord": "^2.9.3", "dayjs": "^1.11.13", "draggable-resizable-vue3": "^1.0.94-beta", "file-saver": "^2.0.5", "graphql": "^16.9.0", "graphql-tag": "^2.12.6", "highlight.js": "^11.10.0", "html-to-image": "^1.11.11", "jszip": "^3.10.1", "lodash": "^4.17.21", "mitt": "^3.0.1", "naive-ui": "^2.39.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.3", "short-unique-id": "^5.2.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "v-wave": "^2.0.0", "vue": "^3.4.38", "vue-advanced-cropper": "^2.8.9", "vue-boring-avatars": "^1.4.0", "vue-highlight-words": "^3.0.1", "vue-qrcode-reader": "^5.5.7", "vue-router": "^4.4.3", "vue3-resize-text": "^0.1.0", "webfontloader": "^1.6.28", "xlsx": "^0.18.5"}, "devDependencies": {"@clack/prompts": "^0.7.0", "@faker-js/faker": "^8.4.1", "@iconify/vue": "^4.1.2", "@nuxt/devtools": "^1.4.1", "@nuxt/test-utils": "^3.14.1", "@nuxtjs/device": "^3.2.2", "@nuxtjs/i18n": "^8.5.1", "@nuxtjs/tailwindcss": "^6.12.1", "@pinia-plugin-persistedstate/nuxt": "^1.2.1", "@pinia/nuxt": "^0.5.4", "@rollup/plugin-graphql": "^2.0.4", "@rushstack/eslint-patch": "^1.10.4", "@stylistic/eslint-plugin-js": "^2.7.2", "@types/dom-view-transitions": "^1.0.5", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.7", "@types/node": "^20.16.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "cypress": "^13.14.1", "eslint": "8.57.0", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-vue": "^9.27.0", "fs-extra": "^11.2.0", "jsdom": "^24.1.3", "json5": "^2.2.3", "npm-run-all": "^4.1.5", "nuxt": "^3.13.0", "nuxt-directus": "^5.6.1", "nuxt-lodash": "^2.5.3", "nuxt-svgo": "^4.0.4", "nuxt-viewport": "^2.1.6", "nuxtjs-naive-ui": "^1.0.2", "picocolors": "^1.0.1", "postcss": "^8.4.43", "prettier": "^3.3.3", "sass": "^1.77.8", "start-server-and-test": "^2.0.5", "tailwind-config-viewer": "^2.0.4", "tailwindcss": "^3.4.10", "taze": "^0.13.9", "ts-node": "^10.9.2", "typescript": "~5.4.5", "vitest": "^1.6.0", "vue-tsc": "^2.1.4"}, "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@3.3.1+sha512.e355f587284d06d2c0c1c2259c68746aa1f1f6cdeedefce543cec206709beeb7951c0a27cdbfb7b5736a576854829af9c660bbe82052192c49f108a546994b71"}