<template>
	<CustomDrawer v-model:show="show" title="Update Template" @close="handleClose" @after-enter="handleAfterEnter">
		<SenderTemplateInfo />
		<n-form ref="formRef" :rules="rules" :model="modelTemplate">
			<n-form-item path="title" label="Judul">
				<n-input
					v-model:value="modelTemplate.title"
					clearable
					type="text"
					placeholder="masukkan judul template"
				/>
			</n-form-item>
			<n-form-item path="content">
				<template #label>
					Konten
					<SenderTemplateExample />
				</template>
				<n-input
					v-model:value="modelTemplate.content"
					clearable
					type="textarea"
					placeholder="masukkan konten template"
					:autosize="{
						minRows: 15
					}"
				/>
			</n-form-item>
		</n-form>
		<template #footer>
			<n-button block type="primary" :loading="loadingSubmit" @click="handleSubmitTemplate">
				Update Template
			</n-button>
		</template>
	</CustomDrawer>
</template>

<script setup>
import { NForm, NFormItem, NInput, NButton, useLoadingBar, useMessage } from "naive-ui"

const props = defineProps({
	agenda: Object,
	template: Object
})

const show = defineModel()
const emit = defineEmits(["close", "after-submit"])

const loadingBar = useLoadingBar()
const message = useMessage()
const { updateItem } = useDirectusItems()

const loadingSubmit = ref(false)
const formRef = ref(null)

const modelTemplate = ref({
	title: null,
	content: null,
	event: null
})

const rules = {
	title: {
		required: true,
		message: "Judul tidak boleh kosong",
		trigger: ["input", "blur"]
	},
	content: {
		required: true,
		message: "Konten tidak boleh kosong",
		trigger: ["input", "blur"]
	}
}

const handleSubmitTemplate = e => {
	e.preventDefault()
	formRef.value?.validate(async errors => {
		if (!errors) {
			await submitTemplate()
		} else {
			console.error(errors)
		}
	})
}

const submitTemplate = async () => {
	loadingBar.start()
	loadingSubmit.value = true
	try {
		modelTemplate.value.event = props.agenda.id
		await updateItem({ collection: "template", id: props.template.id, item: modelTemplate.value })
		message.success("berhasil submit")
		emit("after-submit")
		loadingBar.finish()
	} catch (e) {
		console.error(e)
	} finally {
		loadingSubmit.value = false
	}
}

const handleClose = () => {
	emit("close")
}

const handleAfterEnter = () => {
	const template = { ...props.template }
	modelTemplate.value = {
		title: template.title,
		content: template.content,
		event: template.event
	}
}
</script>

<style></style>
