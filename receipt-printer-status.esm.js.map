{"version": 3, "file": "receipt-printer-status.esm.js", "sources": ["../src/event-emitter.js", "../src/response-buffer.js", "../src/change-observer.js", "../src/info.js", "../src/barcode-scanner.js", "../src/cash-drawer.js", "../src/main.js"], "sourcesContent": ["class EventEmitter {\n    constructor(device) {\n        this._events = {};\n    }\n\n    on(e, f) {\n        this._events[e] = this._events[e] || [];\n        this._events[e].push(f);\n    }\n\n    emit(e, ...args) {\n        let fs = this._events[e];\n        if (fs) {\n            fs.forEach(f => {\n                setTimeout(() => f(...args), 0);\n            });\n        }\n    }        \n}\n\nexport default EventEmitter;", "class ResponseBuffer {\n\n\twindow \t= 0;\n\tcursor \t= 0;\n\tdata \t= new Uint8Array(1024 * 2);       /* 2 KB */ \n\n\tadd(data) {\n\t\tif (this.cursor == this.window) {\n            this.cursor = this.window = 0;\n        }\n\n        this.data.set(new Uint8Array(data.buffer), this.window);\n        this.window += data.byteLength;\n\t}\n\n    get(from, to) {\n        if (!from) {\n            from = 0;\n        }\n        \n        if (!to) {\n            return this.data[from + this.cursor];\n        }\n\n        return this.data.subarray(from + this.cursor, to + this.cursor);\n    }\n\n\n    getWord(target) {\n        return this.data[target + this.cursor] | this.data[target + this.cursor + 1] << 8;\n    }\n\n    getBit(target, position) {\n\t\treturn this.data[target + this.cursor] >> (position) & 1;\n\t}\n\n    getBits(target, pattern) {\n\t\tif (typeof target === 'string') {\n            pattern = target;\n            target = 0;\n        }\n\n        let bits = pattern.split('').map((b,i) => [ 7 - i, b != '.' ? parseInt(b, 10) : null ]).filter(b => b[1] !== null);\n        let value = 0;\n\n        for (let bit of bits) {\n            value |= this.getBit(target, bit[0]) << bit[1];\n        }\n\n        return value;\n    }\n\n\tscanUntilLineFeedNul(window) {\n\t\tlet found = false;\n\t\tlet i;\n\n\t\tfor (i = this.cursor; i < window; i++) {\n\t\t\tif (this.data[i - 1] == 0x0a && this.data[i] == 0x00) {\n\t\t\t\tfound = true;\n\t\t\t\ti++;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn found ? i - this.cursor : null;\n\t}\n\n    scanUntilNul(window) {\n\t\tlet found = false;\n\t\tlet i;\n\n\t\tfor (i = this.cursor; i < window; i++) {\n\t\t\tif (this.data[i] == 0x00) {\n\t\t\t\tfound = true;\n\t\t\t\ti++;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn found ? i - this.cursor : null;\n\t}\n\n\tcheckBit(target, position, value) {\n\t\treturn (this.data[target + this.cursor] >> (position) & 1) === value;\n\t}\n\n\tcheckBits(target, bits) {\n\t\tif (typeof target === 'string') {\n            bits = target;\n            target = 0;\n        }\n\n\t\tif (typeof bits === 'string') {\n\t\t\tbits = bits.split('').map((b,i, a) => [ a.length - i - 1, parseInt(b) ]).filter(b => !isNaN(b[1]))\n\t\t}\n\n\t\tfor (let bit of bits) {\n\t\t\tif (!this.checkBit(target, bit[0], bit[1])) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\tcheckSequence(target, sequence) {\n\t\tif (target instanceof Array) {\n            sequence = target;\n            target = 0;\n        }\n        \n        for (let i = 0; i < sequence.length; i++) {\n\t\t\tif (this.data[target + this.cursor + i] != sequence[i]) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n    get length() {\n        return this.window - this.cursor;\n    }\n}\n\nexport default ResponseBuffer;", "class ChangeObserver {\n    \n    static create(options) {\n\n        let callback = options.callback;\n        let target = options.target;\n\n        return new Proxy (target, {\n            get(target, property, receiver) {\n                if (property === 'target') {\n                    return target;\n                }\n\n                return Reflect.get(target, property, receiver)\n            },\n\n            set(obj, prop, value) {        \n\n                if (obj[prop] !== value) {\n                    obj[prop] = value;\n\n                    callback(obj);\n                }\n\n                return true;\n            }\n        });\n    }\n}\n\nexport default ChangeObserver;", "class ReceiptPrinterInfo {\n\tbuttonPressed = false;\n    online = true;\n    coverOpened = false;\n    paperLoaded = true;\n    paperLow = false;\n}\n\nexport default ReceiptPrinterInfo;", "import EventEmitter from \"./event-emitter.js\";\n\nclass ReceiptPrinterBarcodeScanner {\n\t#printer;\n\t#emitter;\n\t#connected = false;\n    #supported = false;\n\n\tconstructor(printer) {\n\t\tthis.#printer = printer;\n\t\tthis.#emitter = new EventEmitter();\n\n        if (this.#printer._language == 'star-prnt') {\n            this.#supported = true;\n        }\n\t}\n\n    get supported() {\n        return this.#supported;\n    }\n\n\tget connected() {\n\t\treturn this.#connected;\n\t}\n\n\tset connected(value) {\n\t\tif (!this.#connected && value) {\n\t\t\tthis.#emitter.emit('connected');\n\t\t}\n\n\t\tthis.#connected = value;\n        this.#supported = true;\n\t}\n\n\tset barcode(value) {\n\t\tthis.#emitter.emit('barcode', { value });\n\t}\n\n\taddEventListener(n, f) {\n\t\tthis.#emitter.on(n, f);\n\t}\n}\n\nexport default ReceiptPrinterBarcodeScanner;", "import EventEmitter from \"./event-emitter.js\";\n\nclass ReceiptPrinterCashDrawer {\n\t#printer;\n\t#emitter;\n\t#opened = false;\n\n\tconstructor(printer) {\n\t\tthis.#printer = printer;\n\t\tthis.#emitter = new EventEmitter();\n\t}\n\n\topen() {\n\t\tif (!this.#printer.connected) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (this.#printer.language == 'esc-pos') {\n\t\t\tthis.#printer.send([0x1b, 0x70, 0x00, 0x19, 0xfa ]);\n\t\t}\n\n\t\tif (this.#printer.language == 'star-prnt' || this.#printer.language == 'star-line') {\n\t\t\tthis.#printer.send([ 0x1b, 0x07, 0x14, 0x14, 0x07 ]);\n\t\t}\n\t}\n\n    get supported() {\n        return true;\n    }\n\n\tget opened() {\n\t\treturn this.#opened;\n\t}\n\n\tset opened(value) {\n        if (value !== this.#opened) {\n            this.#emitter.emit('update', { opened: value });\n\n            if (value) {\n                this.#emitter.emit('open');\n            } else {\n                this.#emitter.emit('close');\n            }\n        }\n\n\t\tthis.#opened = value;\n\t}\n\n\taddEventListener(n, f) {\n\t\tthis.#emitter.on(n, f);\n\t}\n}\n\nexport default ReceiptPrinterCashDrawer;", "import EventEmitter from \"./event-emitter.js\";\nimport ResponseBuffer from \"./response-buffer.js\";\nimport ChangeObserver from \"./change-observer.js\";\nimport ReceiptPrinterInfo from \"./info.js\";\nimport ReceiptPrinterBarcodeScanner from \"./barcode-scanner.js\";\nimport ReceiptPrinterCashDrawer from \"./cash-drawer.js\";\n\n\nclass ReceiptPrinterStatus {\n\n\tconstructor(options) {\n        this._connected = false;\n\n\t\t/* Defaults */\n\n\t\toptions.language = options.language || 'auto';\n\n\t\t/* Input checks */\n\n\t\tif (typeof options.printer === 'undefined') {\n\t\t\tthrow new Error('You need to provide a printer driver instance');\n\t\t}\n\n\t\tif (Object.getPrototypeOf(options.printer).constructor.name == 'ReceiptPrinterDriver') {\n\t\t\tthrow new Error('Printer driver not supported by this library');\n\t\t}\n\t\n\t\tif (! [ 'esc-pos', 'star-prnt', 'star-line', 'auto' ].includes(options.language)) {\n\t\t\tthrow new Error('Language not supported by this library');\n\t\t}\n\n\t\t/* Initialize properties */\n\n\t\tthis._language = options.language;\n\t\tthis._parsing = false;\n\t\tthis._polling = null;\n\t\tthis._updates = 0;\n\n\t\tthis._pollForUpdates = false;\n\t\tthis._pollForBarcodes = false;\n\n        this._internal = {\n            emitter:    new EventEmitter(),\n            decoder:    new TextDecoder(),       \n\t\t\tbuffer:\t\tnew ResponseBuffer(),\n\t\t\tstatus:\t \tChangeObserver.create({\n\t\t\t\t\t\t\ttarget:\t\tnew ReceiptPrinterInfo, \n\t\t\t\t\t\t\tcallback: \ttarget => {\n\t\t\t\t\t\t\t\tthis._internal.emitter.emit('update', target)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}),\n\n            printer:    options.printer,\n            language:   options.language,\n\t\t\tpolling:\toptions.polling || 'auto',\n\n            callback:   () => {},\n            response:   () => {\n                return new Promise(resolve => {\n                    this._internal.callback = resolve;\n                })\n            }\n        };\n\n\t\tthis.barcodeScanner = new ReceiptPrinterBarcodeScanner(this);\n\t\tthis.cashDrawer = new ReceiptPrinterCashDrawer(this);\n\n\t\t/* Initialize the printer */\n\n\t\tthis.initialize();\n\t}\n\n\tasync initialize() {\n\n\t\t/* Handle responses from the printer */\n\n\t\tthis._internal.printer.addEventListener('data', (data) => this.receive(data));\n\t\t\n\t\tlet listening = await this._internal.printer.listen();\n\n\t\tif (!listening) {\t\n\t\t\tthis._internal.emitter.emit('unsupported');\n\t\t\treturn;\n\t\t}\n\n\t\t/* Handle disconnections */\n\n\t\tthis._internal.printer.addEventListener('disconnected', () => {\n\t\t\tif (this._polling) {\n\t\t\t\tclearInterval(this._polling);\n\t\t\t}\n\n\t\t\tthis._connected = false;\n\t\t\tthis._internal.emitter.emit('disconnected');\n\t\t});\n\n\n\t\t/* Send initialisation commands */\n\n\t\tif (this._language == 'auto') {\n\t\t\tthis.initializeUnknownPrinter();\n\t\t}\n\n\t\tif (this._language == 'star-line' || this._language == 'star-prnt') {\n\t\t\tthis.initializeStarPrinter();\n\t\t}\n\n\t\tif (this._language == 'esc-pos') {\n\t\t\tthis.initializeEscPosPrinter();\n\t\t}\n\n\t\t/* Set timeout in case we do not receive any response to the initialisation */\n\n\t\tsetTimeout(() => {\n\t\t\tif (!this._connected) {\n\t\t\t\tthis._internal.emitter.emit('unsupported');\n\t\t\t}\n\t\t}, 1500);\n    }\n\n\tinitializeUnknownPrinter() {\n\n\t\t/*\n\t\t\tTo detect the printer we send a Star ASB request, and an ESC/POS real-time status request.\n\t\t\tThe Star printer will ignore the ESC/POS command and the ESC/POS printer will ignore\n\t\t\tthe Star command. Both printers will respond and we can determine the printer type\n\t\t\tbased on what the printer responds. \n\t\t*/\n\n\t\t/* ESC ACK SOH = Request ASB (Star) */\n\t\tthis.send([ 0x1b, 0x06, 0x01 ]);\n\n\t\t/* DLT EOT 1 = Transmit real-time status (ESC/POS) */\n\t\tthis.send([ 0x10, 0x04, 0x01 ]);\n\t}\n\n\tinitializeStarPrinter() {\n\n\t\t/* \n\t\t\tIf the language was not known, we already asked for a ASB, so we do not need to do it again \n\t\t*/\n\n\t\tif (this._language == 'star-line' || this._language == 'star-prnt') {\n\t\t\t/* ESC ACK SOH = Request ASB */\n\t\t\tthis.send([ 0x1b, 0x06, 0x01 ]);\n\t\t}\n\n\t\t/* \n\t\t\tOn Star printers we get automatic ASB, or we need to poll. We check after 1 second \n\t\t\tif we have received any automatic ASB response, and if not we start polling.\n\t\t*/\n\n\t\tif (this._internal.polling == 'auto') {\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (this._updates == 1) {\n\t\t\t\t\tthis._pollForUpdates = true;\n\t\t\t\t\tthis.poll();\n\t\t\t\t}\n\t\t\t}, 1000);\n\t\t}\n\n\t\tif (this._internal.polling === true) {\n\t\t\tthis._pollForUpdates = true;\n\t\t\tthis.poll();\n\t\t}\n\t}\n\n\tinitializeEscPosPrinter() {\n\n\t\t/* \n\t\t\tOn ESC/POS printers ASB is turned off by default, but we can turn it on \n\t\t*/\n\n\t\t/* GS a n = Enable Automatic Status Back (ASB) */\n\t\tthis.send([ 0x1d, 0x61, 1 + 2 + 4 + 8 + 64 ]);\n\n\t\t/* DLE DC4 7 1 = Request ASB */\n\t\tthis.send([ 0x10, 0x14, 0x07, 0x01 ]);\n\n\t\tif (this._internal.polling === true) {\n\t\t\tthis._pollForUpdates = true;\n\t\t\tthis.poll();\n\t\t}\n\t}\n\n\n\n    async query(id) {\n        let result, response, found; \n\n        await new Promise(resolve => {\n            setTimeout(resolve, 10);\n        });\n        \n\n\t\t/* StarPRNT and Star Line */\n\n        if (this._language == 'star-prnt' || this._language == 'star-line') {\n\n            switch(id) {\n                case 'manufacturer':\n                    result = \"Star\";\n                    break;\n\n\t\t\t\tcase 'model':\n\t\t\t\t\t/* ESC # * LF NUL = Get printer version */\n                    this.send([ 0x1b, 0x23, 0x2a, 0x0a, 0x00 ]);\n                    response = await this._internal.response();\n\n\t\t\t\t\tfound = response.match(/^(.*)Ver[0-9\\.]+$/);\n                    if (found) {\n                        result = found[1];\n\n\t\t\t\t\t\tswitch(result) {\n\t\t\t\t\t\t\tcase 'POP10':\tresult = 'mPOP'; break;\n\t\t\t\t\t\t}\n                    }\n\n                    break;\n\n\t\t\t\tcase 'firmware':\n\t\t\t\t\t/* ESC # * LF NUL = Get printer version */\n                    this.send([ 0x1b, 0x23, 0x2a, 0x0a, 0x00 ]);\t\t\t\t\t\n                    result = await this._internal.response();\n                    break;\n            }\n        }\n\n\t\t/* StarPRNT */\n\n\t\tif (this._language == 'star-prnt') {\n            switch(id) {\n                case 'serialnumber':\n\t\t\t\t\t/* ESC GS ) I pL pH 49 = Transmit printer information */\n\t\t\t\t\tthis.send([ 0x1b, 0x1d, 0x29, 0x49, 0x01, 0x00, 49 ]);\n\t\t\t\t\tresponse = await this._internal.response();\n\n\t\t\t\t\tfound = response.match(/PrSrN=([0-9]+)[,$]/);\n\t\t\t\t\tif (found) {\n\t\t\t\t\t\tresult = found[1];\n\t\t\t\t\t}\n\n                    break;\n\n                case 'fonts':\n\t\t\t\t\t/* ESC GS ) I pL pH 48 d1 d2 = Transmit all types of multibyte fonts */\n\t\t\t\t\tthis.send([ 0x1b, 0x1d, 0x29, 0x49, 0x03, 0x00, 48, 0, 0 ]);\n                    response = await this._internal.response();\n                    result = response.split(',').filter(i => i);\n                    break;\n\n                case 'interfaces':\n\t\t\t\t\t/* ESC GS ) I pL pH 51 d1 d2 = Transmit installed I/F kind */\n\t\t\t\t\tthis.send([ 0x1b, 0x1d, 0x29, 0x49, 0x03, 0x00, 51, 0, 0 ]);\n\t\t\t\t\tresult = await this._internal.response();\n                    break;\t\t\t\n\t\t\t}\n\t\t}\n\n\t\t/* ESC/POS */\n\n        if (this._language == 'esc-pos') {\n            switch(id) {\n                case 'firmware':\n\t\t\t\t\t/* GS I 65 = Transmit printer firmware version */ \n                    this.send([ 0x1d, 0x49, 65 ]);\n                    result = await this._internal.response();\n                    break;\n                        \n                case 'manufacturer':\n\t\t\t\t\t/* GS I 66 = Transmit printer maker name */ \n                    this.send([ 0x1d, 0x49, 66 ]);\n                    result = await this._internal.response();\n                    break;\n\n                case 'model':\n\t\t\t\t\t/* GS I 67 = Transmit printer model name */ \n                    this.send([ 0x1d, 0x49, 67 ]);\n                    result = await this._internal.response();\n                    break;\n                \n                case 'serialnumber':\n\t\t\t\t\t/* GS I 68 = Transmit printer serial no */ \n                    this.send([ 0x1d, 0x49, 68 ]);\n                    result = await this._internal.response();\n                    break;\n                \n                case 'fonts':\n\t\t\t\t\t/* GS I 69 = Transmit printer font of language for each country */ \n                    this.send([ 0x1d, 0x49, 69 ]);\n\t\t\t\t\tlet response = await this._internal.response();\n\n\t\t\t\t\tif (response) {\n                    \tresult = [ response ]; \n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult = [];\n\t\t\t\t\t}\n\n                    break;\n            }\n        }\n\n        return result;\n    }\n\n\tsend(data) {\n\t\t// console.hex(data);\n\n\t\tthis._internal.printer.print(new Uint8Array(data));\n\t}\n\n\treceive(data) {\n\t\t// console.hex(data);\n\n\t\tthis._internal.buffer.add(data);\n        this.parseResponse();\n    }\n\n\tconnect() {\n        if (this._connected === false) {\n            this._connected = true;\n            this._internal.emitter.emit('connected');\n        }\n\t}\n\n\tpoll() {\n\t\tif (this._polling) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._polling = setInterval(() => {\n\t\t\tif (this._connected) {\t\t\t\n\t\t\t\tif (this._language == 'star-prnt') {\n\t\t\t\t\tif (this._pollForBarcodes) {\n\t\t\t\t\t\tthis.send([ 0x1b, 0x1d, 0x42, 0x32 ]);\t\t/* ESC GS B 2 = Get barcode scanner buffer */\n\t\t\t\t\t}\n\n\t\t\t\t\tif (this._pollForUpdates) {\n\t\t\t\t\t\tthis.send([ 0x1b, 0x06, 0x01 ]);\t\t\t/* ESC ACK SOH = Request ASB */\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (this._language == 'star-line') {\n\t\t\t\t\tif (this._pollForUpdates) {\n\t\t\t\t\t\tthis.send([ 0x1b, 0x06, 0x01 ]);\t\t\t/* ESC ACK SOH = Request ASB */\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (this._language == 'esc-pos') {\n\t\t\t\t\tif (this._pollForUpdates) {\n\t\t\t\t\t\tthis.send([ 0x1d, 0x61, 1 + 2 + 4 + 8 + 64 ]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}, 500);\n\t}\n\n\n\n    parseResponse() {\n        if (this._parsing) {\n            return;\n        }\n\n\t\tthis._parsing = true;\n\n\t\twhile (this._internal.buffer.length) {\n            let skip = 1;\n\n\t\t\t/* If we do not know the language, we need to detect it first */\n\n\t\t\tif (this._language == 'auto') {\n\t\t\t\tthis._language = this.detectLanguage(this._internal.buffer);\n\t\t\t}\n\n\t\t\t/* And once we know, we can parse it */\n\t\t\t\n            if (this._language == 'star-line' || this._language == 'star-prnt') {\n\t\t\t\tskip = this.parseStarResponse(this._internal.buffer);\n            }\n\n            if (this._language == 'esc-pos') {\n\t\t\t\tskip = this.parseEscPosResponse(this._internal.buffer);\n            }\n\n            if (skip == 0) {\n                break;\n            }\n\n            this._internal.buffer.cursor += skip;            \n        }\n\n        this._parsing = false;\n    }\n\n\tdetectLanguage(buffer) {\n\t\tif (buffer.checkBits('0..1..10')) {\n\t\t\tthis.initializeEscPosPrinter();\n\t\t\treturn 'esc-pos';\n\t\t}\n\n\t\tif (buffer.checkBits('0..0...1')) {\n\t\t\tthis.initializeStarPrinter();\n\t\t\treturn 'star-prnt';\n\t\t}\n\n\t\treturn 'unknown';\n\t}\n\n\tparseEscPosResponse(buffer) {\n\t\tlet skip = 0;\n\t\tlet { window, length } = buffer;\n\n\t\t/* ASB */\n\n\t\tif (length >= 4 && buffer.checkBits('0..1..00')) \n\t\t{\n\t\t\tthis._internal.status.online = buffer.checkBits('....0...');\n\t\t\tthis._internal.status.coverOpened = buffer.checkBits('..1.....');\n\t\t\tthis._internal.status.buttonPressed = buffer.checkBits('.1......');\n\t\t\tthis._internal.status.paperLoaded = buffer.checkBits(2, '....00..');\n\t\t\tthis._internal.status.paperLow = buffer.checkBits(2, '......11');\n\n\t\t\tthis.cashDrawer.opened = buffer.checkBits('.....0..');\n\n\t\t\tthis._updates++;\n\t\t\tthis.connect();\n\n\t\t\tskip = 4;\n\t\t}\n\n\t\telse if (length >= 1 && buffer.checkBits('0..1..10')) \n\t\t{\n\t\t\tthis._internal.status.online = buffer.checkBits('....0...');\n\t\t\tthis._internal.status.buttonPressed = buffer.checkBits('.1......');\n\n\t\t\tthis.cashDrawer.opened = buffer.checkBits('.....0..');\n\n\t\t\tskip = 1;\n\t\t}\n\n\t\t/* PrinterInfoB */\n\n\t\telse if (length >= 2 && buffer.get() == 0x5f) \n\t\t{\t\n\t\t\tlet size = buffer.scanUntilNul(window);\n\n\t\t\tif (size !== null) {\n\t\t\t\tlet response = buffer.get(1, size - 1);\n\t\t\t\tthis._internal.callback(this._internal.decoder.decode(response));\n\n\t\t\t\tskip = size;\n\t\t\t}\n\t\t}\n\n\t\telse {\n\t\t\tskip = 1;\n\t\t}\n\n\t\treturn skip;\n\t}\n\n\tparseStarResponse(buffer) {\t\t\n\t\tlet skip = 0;\n\t\tlet { window, length } = buffer;\n\n\t\t/* ASB */\n\n\t\tif (buffer.checkBits('0..0...1')) \n\t\t{\n\t\t\tlet size = buffer.getBits('..3.210.');\n\n\t\t\tif (length >= size) {\n\n\t\t\t\t/* First response from the printer */\n\n\t\t\t\tif (this._updates == 0) {\n\t\t\t\t\tlet version = buffer.getBits(1, '..3.210.');\n\n\t\t\t\t\t/*\n\t\t\t\t\t\tmC-Print2\t\t5\t\tstar-prnt\n\t\t\t\t\t\tmC-Print3\t\t5,6\t\tstar-prnt\n\t\t\t\t\t\tmC-Label3\t\t7\t\tstar-prnt\n\t\t\t\t\t\tmPOP\t\t\t4,5\t\tstar-prnt\n\t\t\t\t\t\tTSP100 \t\t\t3\t\tstar-graphics\n\t\t\t\t\t\tTSP100II    \t3\t\tstar-graphics\n\t\t\t\t\t\tTSP100III\t\t3\t\tstar-graphics\n\t\t\t\t\t\tTSP100IV\t\t6\t\tstar-prnt\n\t\t\t\t\t\tTSP600\t\t\t1,3\t\tstar-line\n\t\t\t\t\t\tTSP650 \t\t\t3\t\tstar-line\n\t\t\t\t\t\tTSP650II\t\t3\t\tstar-line\n\t\t\t\t\t\tTSP700\t\t\t1,3\t\tstar-line\n\t\t\t\t\t\tTSP700II\t\t3\t\tstar-line\n\t\t\t\t\t\tTSP800\t\t\t1,3\t\tstar-line\n\t\t\t\t\t\tTSP800L\t\t\t3\t\tstar-line\n\t\t\t\t\t\tTSP800II\t\t3\t\tstar-line\n\t\t\t\t\t\tTSP1000\t\t\t3\t\tstar-line\n\t\t\t\t\t*/\t\t\n\n\t\t\t\t\t/* Detect if we are using StarPRNT or Star Line */\n\n\t\t\t\t\tif (version >= 4) {\n\t\t\t\t\t\tthis._language = 'star-prnt';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis._language = 'star-line';\n\t\t\t\t\t}\n\n\t\t\t\t\t/* Initialize optional printer features */\n\n\t\t\t\t\tif (this._language == 'star-prnt') {\n\n\t\t\t\t\t\t/* ESC GS B 1 = Barcode scanner status request */\n\t\t\t\t\t\tthis.send([ 0x1b, 0x1d, 0x42, 0x31 ]);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis._internal.status.online = buffer.checkBits(2, '....0...');\n\t\t\t\tthis._internal.status.coverOpened = buffer.checkBits(2, '..1.....');\n\t\t\t\tthis._internal.status.buttonPressed = buffer.checkBits(2, '.1......');\n\t\t\t\tthis._internal.status.paperLoaded = buffer.checkBits(5, '....0...');\n\t\t\t\n\t\t\t\tthis.cashDrawer.opened = buffer.checkBits(2, '.....1..');\n\n\t\t\t\tthis._updates++;\n\t\t\t\tthis.connect();\n\n\t\t\t\tskip = size;\n\t\t\t}\n\t\t}\n\n\t\t/* ESC # * , ... LF NUL = Get printer version */\n\n\t\telse if (length >= 7 && buffer.checkSequence([ 0x1b, 0x23, 0x2a, 0x2c ])) \n\t\t{\n\t\t\tlet size = buffer.scanUntilLineFeedNul(window);\n\n\t\t\tif (size !== null) {\n\t\t\t\tlet response = buffer.get(4, size - 2);\n\t\t\t\tthis._internal.callback(this._internal.decoder.decode(response));\n\n\t\t\t\tskip = size;\n\t\t\t}\n\t\t}\n\t\t\t\n\t\t/* ESC GS ) I pL pH fn k1 k2 ... LF NULL */\n\t\t/* ESC GS ) I pL pH fn ... LF NULL */\n\n\t\telse if (length >= 9 && buffer.checkSequence([ 0x1b, 0x1d, 0x29, 0x49 ])) \n\t\t{\n\t\t\tlet size = buffer.scanUntilLineFeedNul(window);\n\n\t\t\tif (size !== null) {\n\t\t\t\tlet header = buffer.getWord(4);\n\t\t\t\tlet response = buffer.get(6 + header, size - 2);\n\t\t\t\tthis._internal.callback(this._internal.decoder.decode(response));\n\n\t\t\t\tskip = size;\n\t\t\t}\n\t\t}\n\n\t\t/* ESC GS B 1 n = barcode status */\n\n\t\telse if (length >= 5 && buffer.checkSequence([ 0x1b, 0x1d, 0x42, 0x31 ])) \n\t\t{\n\t\t\tif (buffer.get(4) & 0b00000010) {\n\t\t\t\tthis.barcodeScanner.connected = true;\n\n\t\t\t\tthis._pollForBarcodes = true;\n\t\t\t\tthis.poll();\n\t\t\t}\n\n\t\t\tskip = 5;\n\t\t}\n\n\t\t/* ESC GS B 2 n = Barcode buffer */\n\n\t\telse if (length >= 5 && buffer.checkSequence([ 0x1b, 0x1d, 0x42, 0x32 ])) \n\t\t{\n\t\t\tlet size = buffer.getWord(4);\n\n\t\t\tif (size > 0) {\n\t\t\t\tlet response = buffer.get(6, 6 + size - 1);\n\t\t\t\tlet barcodes = this._internal.decoder.decode(response).split('\\r');\n\n\t\t\t\tbarcodes.forEach(barcode => {\n\t\t\t\t\tbarcode = barcode.trim();\n\n\t\t\t\t\tif (barcode != '') {\n\t\t\t\t\t\tthis.barcodeScanner.barcode = barcode;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tskip = 6 + size;\n\t\t}\n\n\t\t/* ESC = start of a response, but not yet complete */\n\n\t\telse if (buffer.get() == 0x1b) \n\t\t{\n\t\t\tskip = 0;\n\t\t}\n\t\n\t\t/* A normal character */\n\n\t\telse {\n\t\t\tskip = 1;\n\t\t}\n\n\t\treturn skip;\n\t}\n\n    addEventListener(n, f) {\n\t\tthis._internal.emitter.on(n, f);\n\t}\n\n    get status() {\n        return this._internal.status.target;\n    }\n\n    get connected() {\n        return this._connected;\n    }\n\n\tget language() {\n\t\treturn this._language;\n\t}\n}\n\nexport default ReceiptPrinterStatus;"], "names": ["EventEmitter", "constructor", "device", "this", "_events", "on", "e", "f", "push", "emit", "args", "fs", "for<PERSON>ach", "setTimeout", "ResponseBuffer", "window", "cursor", "data", "Uint8Array", "add", "set", "buffer", "byteLength", "get", "from", "to", "subarray", "getWord", "target", "getBit", "position", "getBits", "pattern", "bits", "split", "map", "b", "i", "parseInt", "filter", "value", "bit", "scanUntilLineFeedNul", "found", "scanUntilNul", "checkBit", "checkBits", "a", "length", "isNaN", "checkSequence", "sequence", "Array", "ChangeObserver", "create", "options", "callback", "Proxy", "property", "receiver", "Reflect", "obj", "prop", "ReceiptPrinterInfo", "buttonPressed", "online", "coverOpened", "paperLoaded", "paperLow", "ReceiptPrinterBarcodeScanner", "printer", "emitter", "connected", "supported", "_language", "barcode", "addEventListener", "n", "ReceiptPrinterCashDrawer", "opened", "open", "language", "send", "ReceiptPrinterStatus", "_connected", "Error", "Object", "getPrototypeOf", "name", "includes", "_parsing", "_polling", "_updates", "_pollForUpdates", "_pollForBarcodes", "_internal", "decoder", "TextDecoder", "status", "polling", "response", "Promise", "resolve", "barcodeScanner", "cashDrawer", "initialize", "receive", "listen", "clearInterval", "initializeUnknownPrinter", "initializeStarPrinter", "initializeEscPosPrinter", "poll", "query", "id", "result", "match", "print", "parseResponse", "connect", "setInterval", "skip", "detectLanguage", "parseStarResponse", "parseEscPosResponse", "size", "decode", "version", "header", "trim"], "mappings": "AAAA,MAAMA,aACF,WAAAC,CAAYC,GACRC,KAAKC,QAAU,EAClB,CAED,EAAAC,CAAGC,EAAGC,GACFJ,KAAKC,QAAQE,GAAKH,KAAKC,QAAQE,IAAM,GACrCH,KAAKC,QAAQE,GAAGE,KAAKD,EACxB,CAED,IAAAE,CAAKH,KAAMI,GACP,IAAIC,EAAKR,KAAKC,QAAQE,GAClBK,GACAA,EAAGC,SAAQL,IACPM,YAAW,IAAMN,KAAKG,IAAO,EAAE,GAG1C,ECjBL,MAAMI,eAELC,OAAU,EACVC,OAAU,EACVC,KAAQ,IAAIC,WAAW,MAEvB,GAAAC,CAAIF,GACCd,KAAKa,QAAUb,KAAKY,SACdZ,KAAKa,OAASb,KAAKY,OAAS,GAGhCZ,KAAKc,KAAKG,IAAI,IAAIF,WAAWD,EAAKI,QAASlB,KAAKY,QAChDZ,KAAKY,QAAUE,EAAKK,UAC1B,CAEE,GAAAC,CAAIC,EAAMC,GAKN,OAJKD,IACDA,EAAO,GAGNC,EAIEtB,KAAKc,KAAKS,SAASF,EAAOrB,KAAKa,OAAQS,EAAKtB,KAAKa,QAH7Cb,KAAKc,KAAKO,EAAOrB,KAAKa,OAIpC,CAGD,OAAAW,CAAQC,GACJ,OAAOzB,KAAKc,KAAKW,EAASzB,KAAKa,QAAUb,KAAKc,KAAKW,EAASzB,KAAKa,OAAS,IAAM,CACnF,CAED,MAAAa,CAAOD,EAAQE,GACjB,OAAO3B,KAAKc,KAAKW,EAASzB,KAAKa,SAAY,EAAY,CACvD,CAEE,OAAAe,CAAQH,EAAQI,GACI,iBAAXJ,IACDI,EAAUJ,EACVA,EAAS,GAGb,IAAIK,EAAOD,EAAQE,MAAM,IAAIC,KAAI,CAACC,EAAEC,IAAM,CAAE,EAAIA,EAAQ,KAALD,EAAWE,SAASF,EAAG,IAAM,QAAQG,QAAOH,GAAc,OAATA,EAAE,KAClGI,EAAQ,EAEZ,IAAK,IAAIC,KAAOR,EACZO,GAASrC,KAAK0B,OAAOD,EAAQa,EAAI,KAAOA,EAAI,GAGhD,OAAOD,CACV,CAEJ,oBAAAE,CAAqB3B,GACpB,IACIsB,EADAM,GAAQ,EAGZ,IAAKN,EAAIlC,KAAKa,OAAQqB,EAAItB,EAAQsB,IACjC,GAAwB,IAApBlC,KAAKc,KAAKoB,EAAI,IAA8B,GAAhBlC,KAAKc,KAAKoB,GAAY,CACrDM,GAAQ,EACRN,IACA,KACA,CAGF,OAAOM,EAAQN,EAAIlC,KAAKa,OAAS,IACjC,CAEE,YAAA4B,CAAa7B,GACf,IACIsB,EADAM,GAAQ,EAGZ,IAAKN,EAAIlC,KAAKa,OAAQqB,EAAItB,EAAQsB,IACjC,GAAoB,GAAhBlC,KAAKc,KAAKoB,GAAY,CACzBM,GAAQ,EACRN,IACA,KACA,CAGF,OAAOM,EAAQN,EAAIlC,KAAKa,OAAS,IACjC,CAED,QAAA6B,CAASjB,EAAQE,EAAUU,GAC1B,OAAQrC,KAAKc,KAAKW,EAASzB,KAAKa,WAAwB,KAAOwB,CAC/D,CAED,SAAAM,CAAUlB,EAAQK,GACK,iBAAXL,IACDK,EAAOL,EACPA,EAAS,GAGC,iBAATK,IACVA,EAAOA,EAAKC,MAAM,IAAIC,KAAI,CAACC,EAAEC,EAAGU,IAAM,CAAEA,EAAEC,OAASX,EAAI,EAAGC,SAASF,MAAMG,QAAOH,IAAMa,MAAMb,EAAE,OAG/F,IAAK,IAAIK,KAAOR,EACf,IAAK9B,KAAK0C,SAASjB,EAAQa,EAAI,GAAIA,EAAI,IACtC,OAAO,EAIT,OAAO,CACP,CAED,aAAAS,CAActB,EAAQuB,GACjBvB,aAAkBwB,QACZD,EAAWvB,EACXA,EAAS,GAGb,IAAK,IAAIS,EAAI,EAAGA,EAAIc,EAASH,OAAQX,IAC1C,GAAIlC,KAAKc,KAAKW,EAASzB,KAAKa,OAASqB,IAAMc,EAASd,GACnD,OAAO,EAIT,OAAO,CACP,CAEE,UAAIW,GACA,OAAO7C,KAAKY,OAASZ,KAAKa,MAC7B,EC1HL,MAAMqC,eAEF,aAAOC,CAAOC,GAEV,IAAIC,EAAWD,EAAQC,SACnB5B,EAAS2B,EAAQ3B,OAErB,OAAO,IAAI6B,MAAO7B,EAAQ,CACtBL,IAAG,CAACK,EAAQ8B,EAAUC,IACD,WAAbD,EACO9B,EAGJgC,QAAQrC,IAAIK,EAAQ8B,EAAUC,GAGzCvC,IAAG,CAACyC,EAAKC,EAAMtB,KAEPqB,EAAIC,KAAUtB,IACdqB,EAAIC,GAAQtB,EAEZgB,EAASK,KAGN,IAGlB,EC3BL,MAAME,mBACLC,eAAgB,EACbC,QAAS,EACTC,aAAc,EACdC,aAAc,EACdC,UAAW,ECHf,MAAMC,6BACLC,GACAC,GACAC,IAAa,EACVC,IAAa,EAEhB,WAAAxE,CAAYqE,GACXnE,MAAKmE,EAAWA,EAChBnE,MAAKoE,EAAW,IAAIvE,aAEiB,aAA3BG,MAAKmE,EAASI,YACdvE,MAAKsE,GAAa,EAE5B,CAEE,aAAIA,GACA,OAAOtE,MAAKsE,CACf,CAEJ,aAAID,GACH,OAAOrE,MAAKqE,CACZ,CAED,aAAIA,CAAUhC,IACRrC,MAAKqE,GAAchC,GACvBrC,MAAKoE,EAAS9D,KAAK,aAGpBN,MAAKqE,EAAahC,EACZrC,MAAKsE,GAAa,CACxB,CAED,WAAIE,CAAQnC,GACXrC,MAAKoE,EAAS9D,KAAK,UAAW,CAAE+B,SAChC,CAED,gBAAAoC,CAAiBC,EAAGtE,GACnBJ,MAAKoE,EAASlE,GAAGwE,EAAGtE,EACpB,ECtCF,MAAMuE,yBACLR,GACAC,GACAQ,IAAU,EAEV,WAAA9E,CAAYqE,GACXnE,MAAKmE,EAAWA,EAChBnE,MAAKoE,EAAW,IAAIvE,YACpB,CAED,IAAAgF,GACM7E,MAAKmE,EAASE,YAIW,WAA1BrE,MAAKmE,EAASW,UACjB9E,MAAKmE,EAASY,KAAK,CAAC,GAAM,IAAM,EAAM,GAAM,MAGf,aAA1B/E,MAAKmE,EAASW,UAAqD,aAA1B9E,MAAKmE,EAASW,UAC1D9E,MAAKmE,EAASY,KAAK,CAAE,GAAM,EAAM,GAAM,GAAM,IAE9C,CAEE,aAAIT,GACA,OAAO,CACV,CAEJ,UAAIM,GACH,OAAO5E,MAAK4E,CACZ,CAED,UAAIA,CAAOvC,GACAA,IAAUrC,MAAK4E,IACf5E,MAAKoE,EAAS9D,KAAK,SAAU,CAAEsE,OAAQvC,IAEnCA,EACArC,MAAKoE,EAAS9D,KAAK,QAEnBN,MAAKoE,EAAS9D,KAAK,UAIjCN,MAAK4E,EAAUvC,CACf,CAED,gBAAAoC,CAAiBC,EAAGtE,GACnBJ,MAAKoE,EAASlE,GAAGwE,EAAGtE,EACpB,EC1CF,MAAM4E,qBAEL,WAAAlF,CAAYsD,GASX,GARMpD,KAAKiF,YAAa,EAIxB7B,EAAQ0B,SAAW1B,EAAQ0B,UAAY,YAIR,IAApB1B,EAAQe,QAClB,MAAM,IAAIe,MAAM,iDAGjB,GAA+D,wBAA3DC,OAAOC,eAAehC,EAAQe,SAASrE,YAAYuF,KACtD,MAAM,IAAIH,MAAM,gDAGjB,IAAM,CAAE,UAAW,YAAa,YAAa,QAASI,SAASlC,EAAQ0B,UACtE,MAAM,IAAII,MAAM,0CAKjBlF,KAAKuE,UAAYnB,EAAQ0B,SACzB9E,KAAKuF,UAAW,EAChBvF,KAAKwF,SAAW,KAChBxF,KAAKyF,SAAW,EAEhBzF,KAAK0F,iBAAkB,EACvB1F,KAAK2F,kBAAmB,EAElB3F,KAAK4F,UAAY,CACbxB,QAAY,IAAIvE,aAChBgG,QAAY,IAAIC,YACzB5E,OAAS,IAAIP,eACboF,OAAU7C,eAAeC,OAAO,CAC5B1B,OAAS,IAAImC,mBACbP,SAAW5B,IACVzB,KAAK4F,UAAUxB,QAAQ9D,KAAK,SAAUmB,EAAO,IAIzC0C,QAAYf,EAAQe,QACpBW,SAAY1B,EAAQ0B,SAC7BkB,QAAS5C,EAAQ4C,SAAW,OAEnB3C,SAAY,OACZ4C,SAAY,IACD,IAAIC,SAAQC,IACfnG,KAAK4F,UAAUvC,SAAW8C,CAAO,KAKnDnG,KAAKoG,eAAiB,IAAIlC,6BAA6BlE,MACvDA,KAAKqG,WAAa,IAAI1B,yBAAyB3E,MAI/CA,KAAKsG,YACL,CAED,gBAAMA,GAILtG,KAAK4F,UAAUzB,QAAQM,iBAAiB,QAAS3D,GAASd,KAAKuG,QAAQzF,WAEjDd,KAAK4F,UAAUzB,QAAQqC,UAS7CxG,KAAK4F,UAAUzB,QAAQM,iBAAiB,gBAAgB,KACnDzE,KAAKwF,UACRiB,cAAczG,KAAKwF,UAGpBxF,KAAKiF,YAAa,EAClBjF,KAAK4F,UAAUxB,QAAQ9D,KAAK,eAAe,IAMtB,QAAlBN,KAAKuE,WACRvE,KAAK0G,2BAGgB,aAAlB1G,KAAKuE,WAA8C,aAAlBvE,KAAKuE,WACzCvE,KAAK2G,wBAGgB,WAAlB3G,KAAKuE,WACRvE,KAAK4G,0BAKNlG,YAAW,KACLV,KAAKiF,YACTjF,KAAK4F,UAAUxB,QAAQ9D,KAAK,cAC5B,GACC,OApCFN,KAAK4F,UAAUxB,QAAQ9D,KAAK,cAqC1B,CAEJ,wBAAAoG,GAUC1G,KAAK+E,KAAK,CAAE,GAAM,EAAM,IAGxB/E,KAAK+E,KAAK,CAAE,GAAM,EAAM,GACxB,CAED,qBAAA4B,GAMuB,aAAlB3G,KAAKuE,WAA8C,aAAlBvE,KAAKuE,WAEzCvE,KAAK+E,KAAK,CAAE,GAAM,EAAM,IAQK,QAA1B/E,KAAK4F,UAAUI,SAClBtF,YAAW,KACW,GAAjBV,KAAKyF,WACRzF,KAAK0F,iBAAkB,EACvB1F,KAAK6G,OACL,GACC,MAG2B,IAA3B7G,KAAK4F,UAAUI,UAClBhG,KAAK0F,iBAAkB,EACvB1F,KAAK6G,OAEN,CAED,uBAAAD,GAOC5G,KAAK+E,KAAK,CAAE,GAAM,GAAM,KAGxB/E,KAAK+E,KAAK,CAAE,GAAM,GAAM,EAAM,KAEC,IAA3B/E,KAAK4F,UAAUI,UAClBhG,KAAK0F,iBAAkB,EACvB1F,KAAK6G,OAEN,CAIE,WAAMC,CAAMC,GACR,IAAIC,EAAQf,EAAUzD,EAStB,SAPM,IAAI0D,SAAQC,IACdzF,WAAWyF,EAAS,GAAG,IAML,aAAlBnG,KAAKuE,WAA8C,aAAlBvE,KAAKuE,UAEtC,OAAOwC,GACH,IAAK,eACDC,EAAS,OACT,MAEhB,IAAK,QAMW,GAJAhH,KAAK+E,KAAK,CAAE,GAAM,GAAM,GAAM,GAAM,IACpCkB,QAAiBjG,KAAK4F,UAAUK,WAE/CzD,EAAQyD,EAASgB,MAAM,qBACJzE,IACAwE,EAASxE,EAAM,GAG3B,UADCwE,GACQA,EAAS,OAIV,MAEhB,IAAK,WAEWhH,KAAK+E,KAAK,CAAE,GAAM,GAAM,GAAM,GAAM,IACpCiC,QAAehH,KAAK4F,UAAUK,WAOhD,GAAsB,aAAlBjG,KAAKuE,UACC,OAAOwC,GACH,IAAK,eAEhB/G,KAAK+E,KAAK,CAAE,GAAM,GAAM,GAAM,GAAM,EAAM,EAAM,KAChDkB,QAAiBjG,KAAK4F,UAAUK,WAEhCzD,EAAQyD,EAASgB,MAAM,sBACnBzE,IACHwE,EAASxE,EAAM,IAGD,MAEJ,IAAK,QAEhBxC,KAAK+E,KAAK,CAAE,GAAM,GAAM,GAAM,GAAM,EAAM,EAAM,GAAI,EAAG,IACxCkB,QAAiBjG,KAAK4F,UAAUK,WAChCe,EAASf,EAASlE,MAAM,KAAKK,QAAOF,GAAKA,IACzC,MAEJ,IAAK,aAEhBlC,KAAK+E,KAAK,CAAE,GAAM,GAAM,GAAM,GAAM,EAAM,EAAM,GAAI,EAAG,IACvDiC,QAAehH,KAAK4F,UAAUK,WAO3B,GAAsB,WAAlBjG,KAAKuE,UACL,OAAOwC,GACH,IAAK,WAED/G,KAAK+E,KAAK,CAAE,GAAM,GAAM,KACxBiC,QAAehH,KAAK4F,UAAUK,WAC9B,MAEJ,IAAK,eAEDjG,KAAK+E,KAAK,CAAE,GAAM,GAAM,KACxBiC,QAAehH,KAAK4F,UAAUK,WAC9B,MAEJ,IAAK,QAEDjG,KAAK+E,KAAK,CAAE,GAAM,GAAM,KACxBiC,QAAehH,KAAK4F,UAAUK,WAC9B,MAEJ,IAAK,eAEDjG,KAAK+E,KAAK,CAAE,GAAM,GAAM,KACxBiC,QAAehH,KAAK4F,UAAUK,WAC9B,MAEJ,IAAK,QAEDjG,KAAK+E,KAAK,CAAE,GAAM,GAAM,KACvC,IAAIkB,QAAiBjG,KAAK4F,UAAUK,WAGpBe,EADZf,EACqB,CAAEA,GAEjB,GAOP,OAAOe,CACV,CAEJ,IAAAjC,CAAKjE,GAGJd,KAAK4F,UAAUzB,QAAQ+C,MAAM,IAAInG,WAAWD,GAC5C,CAED,OAAAyF,CAAQzF,GAGPd,KAAK4F,UAAU1E,OAAOF,IAAIF,GACpBd,KAAKmH,eACR,CAEJ,OAAAC,IAC+B,IAApBpH,KAAKiF,aACLjF,KAAKiF,YAAa,EAClBjF,KAAK4F,UAAUxB,QAAQ9D,KAAK,aAEtC,CAED,IAAAuG,GACK7G,KAAKwF,WAITxF,KAAKwF,SAAW6B,aAAY,KACvBrH,KAAKiF,aACc,aAAlBjF,KAAKuE,YACJvE,KAAK2F,kBACR3F,KAAK+E,KAAK,CAAE,GAAM,GAAM,GAAM,KAG3B/E,KAAK0F,iBACR1F,KAAK+E,KAAK,CAAE,GAAM,EAAM,KAIJ,aAAlB/E,KAAKuE,WACJvE,KAAK0F,iBACR1F,KAAK+E,KAAK,CAAE,GAAM,EAAM,IAIJ,WAAlB/E,KAAKuE,WACJvE,KAAK0F,iBACR1F,KAAK+E,KAAK,CAAE,GAAM,GAAM,KAG1B,GACC,KACH,CAIE,aAAAoC,GACI,IAAInH,KAAKuF,SAAT,CAMN,IAFAvF,KAAKuF,UAAW,EAETvF,KAAK4F,UAAU1E,OAAO2B,QAAQ,CAC3B,IAAIyE,EAAO,EAkBX,GAda,QAAlBtH,KAAKuE,YACRvE,KAAKuE,UAAYvE,KAAKuH,eAAevH,KAAK4F,UAAU1E,SAKtB,aAAlBlB,KAAKuE,WAA8C,aAAlBvE,KAAKuE,YAClD+C,EAAOtH,KAAKwH,kBAAkBxH,KAAK4F,UAAU1E,SAGf,WAAlBlB,KAAKuE,YACjB+C,EAAOtH,KAAKyH,oBAAoBzH,KAAK4F,UAAU1E,SAG3B,GAARoG,EACA,MAGJtH,KAAK4F,UAAU1E,OAAOL,QAAUyG,CACnC,CAEDtH,KAAKuF,UAAW,CA9Bf,CA+BJ,CAEJ,cAAAgC,CAAerG,GACd,OAAIA,EAAOyB,UAAU,aACpB3C,KAAK4G,0BACE,WAGJ1F,EAAOyB,UAAU,aACpB3C,KAAK2G,wBACE,aAGD,SACP,CAED,mBAAAc,CAAoBvG,GACnB,IAAIoG,EAAO,GACP1G,OAAEA,EAAMiC,OAAEA,GAAW3B,EAIzB,GAAI2B,GAAU,GAAK3B,EAAOyB,UAAU,YAEnC3C,KAAK4F,UAAUG,OAAOjC,OAAS5C,EAAOyB,UAAU,YAChD3C,KAAK4F,UAAUG,OAAOhC,YAAc7C,EAAOyB,UAAU,YACrD3C,KAAK4F,UAAUG,OAAOlC,cAAgB3C,EAAOyB,UAAU,YACvD3C,KAAK4F,UAAUG,OAAO/B,YAAc9C,EAAOyB,UAAU,EAAG,YACxD3C,KAAK4F,UAAUG,OAAO9B,SAAW/C,EAAOyB,UAAU,EAAG,YAErD3C,KAAKqG,WAAWzB,OAAS1D,EAAOyB,UAAU,YAE1C3C,KAAKyF,WACLzF,KAAKoH,UAELE,EAAO,OAGH,GAAIzE,GAAU,GAAK3B,EAAOyB,UAAU,YAExC3C,KAAK4F,UAAUG,OAAOjC,OAAS5C,EAAOyB,UAAU,YAChD3C,KAAK4F,UAAUG,OAAOlC,cAAgB3C,EAAOyB,UAAU,YAEvD3C,KAAKqG,WAAWzB,OAAS1D,EAAOyB,UAAU,YAE1C2E,EAAO,OAKH,GAAIzE,GAAU,GAAqB,IAAhB3B,EAAOE,MAC/B,CACC,IAAIsG,EAAOxG,EAAOuB,aAAa7B,GAE/B,GAAa,OAAT8G,EAAe,CAClB,IAAIzB,EAAW/E,EAAOE,IAAI,EAAGsG,EAAO,GACpC1H,KAAK4F,UAAUvC,SAASrD,KAAK4F,UAAUC,QAAQ8B,OAAO1B,IAEtDqB,EAAOI,CACP,CACD,MAGAJ,EAAO,EAGR,OAAOA,CACP,CAED,iBAAAE,CAAkBtG,GACjB,IAAIoG,EAAO,GACP1G,OAAEA,EAAMiC,OAAEA,GAAW3B,EAIzB,GAAIA,EAAOyB,UAAU,YACrB,CACC,IAAI+E,EAAOxG,EAAOU,QAAQ,YAE1B,GAAIiB,GAAU6E,EAAM,CAInB,GAAqB,GAAjB1H,KAAKyF,SAAe,CACvB,IAAImC,EAAU1G,EAAOU,QAAQ,EAAG,YAyB/B5B,KAAKuE,UADFqD,GAAW,EACG,YAEA,YAKI,aAAlB5H,KAAKuE,WAGRvE,KAAK+E,KAAK,CAAE,GAAM,GAAM,GAAM,IAE/B,CAED/E,KAAK4F,UAAUG,OAAOjC,OAAS5C,EAAOyB,UAAU,EAAG,YACnD3C,KAAK4F,UAAUG,OAAOhC,YAAc7C,EAAOyB,UAAU,EAAG,YACxD3C,KAAK4F,UAAUG,OAAOlC,cAAgB3C,EAAOyB,UAAU,EAAG,YAC1D3C,KAAK4F,UAAUG,OAAO/B,YAAc9C,EAAOyB,UAAU,EAAG,YAExD3C,KAAKqG,WAAWzB,OAAS1D,EAAOyB,UAAU,EAAG,YAE7C3C,KAAKyF,WACLzF,KAAKoH,UAELE,EAAOI,CACP,CACD,MAII,GAAI7E,GAAU,GAAK3B,EAAO6B,cAAc,CAAE,GAAM,GAAM,GAAM,KACjE,CACC,IAAI2E,EAAOxG,EAAOqB,qBAAqB3B,GAEvC,GAAa,OAAT8G,EAAe,CAClB,IAAIzB,EAAW/E,EAAOE,IAAI,EAAGsG,EAAO,GACpC1H,KAAK4F,UAAUvC,SAASrD,KAAK4F,UAAUC,QAAQ8B,OAAO1B,IAEtDqB,EAAOI,CACP,CACD,MAKI,GAAI7E,GAAU,GAAK3B,EAAO6B,cAAc,CAAE,GAAM,GAAM,GAAM,KACjE,CACC,IAAI2E,EAAOxG,EAAOqB,qBAAqB3B,GAEvC,GAAa,OAAT8G,EAAe,CAClB,IAAIG,EAAS3G,EAAOM,QAAQ,GACxByE,EAAW/E,EAAOE,IAAI,EAAIyG,EAAQH,EAAO,GAC7C1H,KAAK4F,UAAUvC,SAASrD,KAAK4F,UAAUC,QAAQ8B,OAAO1B,IAEtDqB,EAAOI,CACP,CACD,MAII,GAAI7E,GAAU,GAAK3B,EAAO6B,cAAc,CAAE,GAAM,GAAM,GAAM,KAE5C,EAAhB7B,EAAOE,IAAI,KACdpB,KAAKoG,eAAe/B,WAAY,EAEhCrE,KAAK2F,kBAAmB,EACxB3F,KAAK6G,QAGNS,EAAO,OAKH,GAAIzE,GAAU,GAAK3B,EAAO6B,cAAc,CAAE,GAAM,GAAM,GAAM,KACjE,CACC,IAAI2E,EAAOxG,EAAOM,QAAQ,GAE1B,GAAIkG,EAAO,EAAG,CACb,IAAIzB,EAAW/E,EAAOE,IAAI,EAAG,EAAIsG,EAAO,GACzB1H,KAAK4F,UAAUC,QAAQ8B,OAAO1B,GAAUlE,MAAM,MAEpDtB,SAAQ+D,IAGD,KAFfA,EAAUA,EAAQsD,UAGjB9H,KAAKoG,eAAe5B,QAAUA,EAC9B,GAEF,CAED8C,EAAO,EAAII,CACX,MAMAJ,EAFwB,IAAhBpG,EAAOE,MAER,EAMA,EAGR,OAAOkG,CACP,CAEE,gBAAA7C,CAAiBC,EAAGtE,GACtBJ,KAAK4F,UAAUxB,QAAQlE,GAAGwE,EAAGtE,EAC7B,CAEE,UAAI2F,GACA,OAAO/F,KAAK4F,UAAUG,OAAOtE,MAChC,CAED,aAAI4C,GACA,OAAOrE,KAAKiF,UACf,CAEJ,YAAIH,GACH,OAAO9E,KAAKuE,SACZ"}