<template>
	<n-table :bordered="false" :single-line="false">
		<thead>
			<tr>
				<th v-for="n in 5" :key="'th' + n"><n-skeleton text /></th>
			</tr>
		</thead>
		<tbody>
			<tr v-for="n in 10" :key="'tr' + n">
				<td v-for="n in 5" :key="'td' + n"><n-skeleton text /></td>
			</tr>
		</tbody>
	</n-table>
</template>

<script setup>
import { NTable, NSkeleton } from "naive-ui"
</script>

<style></style>
