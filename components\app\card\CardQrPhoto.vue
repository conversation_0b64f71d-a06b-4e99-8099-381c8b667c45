<template>
	<div class="qr-card p-0 flex flex-col h-full" :style="{ backgroundColor: cardInputModel.backgroundColor }">
		<div class="p-0 m-0 w-full aspect-[3/2] overflow-hidden bg-cover bg-center relative">
			<n-image
				class="absolute inset-0 z-0 image-card aspect-[3/2]"
				width="100%"
				object-fit="cover"
				:src="
					urlPhoto
						? urlPhoto
						: 'https://directus.bagimomen.my.id/assets/46df6859-8aec-4b21-ae04-7350dc4b00ac/bannerDetailBlog.webp'
				"
			/>
			<div class="absolute inset-0 bg-gradient-to-t from-black/[.7] z-10 pointer-events-none"></div>
			<div
				v-if="level > 1"
				v-resize-text="{
					ratio: 0.35,
					minFontSize: 10,
					maxFontSize: 500,
					delay: 200
				}"
				class="absolute w-[10%] top-[5%] right-[5%] bg-[#d3c861] rounded-full z-20 text-center"
				style="color: #000000"
			>
				{{ level > 1 ? (level == 2 ? "VIP" : "VVIP") : "" }}
			</div>
			<div
				v-resize-text="{
					ratio: 1.3,
					minFontSize: 10,
					maxFontSize: 500,
					delay: 200
				}"
				class="relative h-full w-full flex items-end justify-center z-20 pointer-events-none"
				:style="{
					padding: '5%',
					fontFamily: cardInputModel.nameFont,
					color: cardInputModel.nameColor
				}"
			>
				{{ cardInputModel.name }}
			</div>
		</div>
		<div
			v-resize-text="{
				ratio: 1.8,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			class="text-center"
			:style="{
				padding: '2%',
				backgroundColor: cardInputModel.titleBgColor,
				color: cardInputModel.titleColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ cardInputModel.title }}
		</div>

		<n-flex vertical justify="space-between" class="p-0 flex-grow" style="margin-top: 5%">
			<n-row gutter="2" style="padding: 4%; margin-bottom: 5%">
				<n-col :span="14">
					<div
						v-resize-text="{
							ratio: 1.8,
							minFontSize: 10,
							maxFontSize: 500,
							delay: 200
						}"
						:style="{
							marginBottom: '5%',
							color: cardInputModel.textColor,
							fontFamily: cardInputModel.textFont
						}"
					>
						<template v-if="cardInputModel.language == 0">
							Kepada Yth.
							<br />
							Bapak/Ibu/Saudara/i
						</template>
						<template v-if="cardInputModel.language == 1">
							Dear.
							<br />
							Mr/Mrs/Ms
						</template>
					</div>
					<n-grid cols="6">
						<n-grid-item span="1">
							<Icon
								icon="icon-park-solid:people"
								width="50%"
								height="80%"
								:color="cardInputModel.textColor"
							/>
						</n-grid-item>
						<n-grid-item
							v-resize-text="{
								ratio: 1.4,
								minFontSize: 10,
								maxFontSize: 500,
								delay: 200
							}"
							span="5"
							:style="{
								marginBottom: '5%',
								color: cardInputModel.textColor,
								fontFamily: cardInputModel.textFont
							}"
						>
							{{ name }}
						</n-grid-item>
						<n-grid-item span="1">
							<Icon
								icon="lets-icons:date-fill"
								width="50%"
								height="80%"
								:color="cardInputModel.textColor"
							/>
						</n-grid-item>
						<n-grid-item
							v-resize-text="{
								ratio: 1.4,
								minFontSize: 10,
								maxFontSize: 500,
								delay: 200
							}"
							span="5"
							:style="{
								marginBottom: '5%',
								color: cardInputModel.textColor,
								fontFamily: cardInputModel.textFont
							}"
						>
							{{ cardInputModel.date }}
							<br />
							{{ cardInputModel.session == 0 ? cardInputModel.time : shift }}
						</n-grid-item>
						<n-grid-item span="1">
							<Icon
								icon="lets-icons:pin-alt-fill"
								width="50%"
								height="80%"
								:color="cardInputModel.textColor"
							/>
						</n-grid-item>
						<n-grid-item
							v-resize-text="{
								ratio: 1.4,
								minFontSize: 10,
								maxFontSize: 500,
								delay: 200
							}"
							span="5"
							:style="{
								marginBottom: '5%',
								color: cardInputModel.textColor,
								fontFamily: cardInputModel.textFont
							}"
						>
							{{ cardInputModel.location }}
						</n-grid-item>
					</n-grid>
				</n-col>
				<n-col :span="10" class="">
					<div
						ref="qrsize"
						class="w-full rounded-lg text-center justify-center align-center px-2 pt-2 bg-white mx-auto border-2 border-black"
					>
						<n-qr-code
							class="p-0 m-0"
							:value="qr"
							type="svg"
							:size="qrWidth"
							:icon-src="iconSrc"
							icon-background-color="#ffffff"
							error-correction-level="H"
						/>
					</div>
				</n-col>
			</n-row>

			<div class="mt-auto" style="padding-left: 10%; padding-right: 10%; margin-bottom: 8%">
				<n-p
					v-resize-text="{
						ratio: 3.2,
						minFontSize: 10,
						maxFontSize: 500,
						delay: 200
					}"
					class="text-center font-light"
					:style="{
						marginBottom: '2%',
						color: cardInputModel.textColor,
						fontFamily: cardInputModel.textFont
					}"
				>
					{{ cardInputModel.info }}
				</n-p>
				<n-p
					v-resize-text="{
						ratio: 4.3,
						minFontSize: 10,
						maxFontSize: 500,
						delay: 200
					}"
					class="text-center font-light self-end"
					:style="{
						color: cardInputModel.textColor,
						fontFamily: cardInputModel.textFont
					}"
				>
					Powered by BAGIMOMEN
				</n-p>
			</div>
		</n-flex>
	</div>
</template>

<script setup>
import { NImage, NGrid, NGridItem, NRow, NCol, NQrCode, NFlex, NP } from "naive-ui"
import VueResizeText from "vue3-resize-text"
import { Icon } from "@iconify/vue"

defineProps({
	cardInputModel: {
		type: Object,
		required: true
	},
	urlPhoto: {
		type: String,
		required: false
	},
	name: {
		type: String,
		required: true
	},
	qr: {
		type: String,
		required: true
	},
	level: {
		type: Number,
		default: 1
	},
	shift: {
		type: String,
		default: ""
	}
})

const vResizeText = VueResizeText.ResizeText

const qrsize = ref(null)
const { width: qrWidth } = useElementSize(qrsize)

const iconSrc = "https://directus.bagimomen.my.id/assets/74d78007-c9b9-4cff-b099-4e85df2605ee/<EMAIL>"
</script>

<style></style>
