<template>
	<n-card
		v-wave
		content-style="padding: 12px;"
		:style="viewport.isLessThan('tablet') ? 'width: 100% !important' : 'width: 49% !important'"
	>
		<div class="flex items-center">
			<div class="flex-1 cursor-pointer" @click="goto()">
				<n-descriptions label-placement="top">
					<n-descriptions-item :label="agenda.title">
						<n-text depth="3">
							{{ agenda.location || "Belum Ditentukan" }}
						</n-text>
					</n-descriptions-item>
				</n-descriptions>
			</div>
			<div v-if="isAdmin" class="flex-none">
				<n-dropdown
					:show="showDropdownRef"
					trigger="click"
					:options="options"
					@select="handleSelect"
					@clickoutside="close"
				>
					<n-button quaternary @click="handleClick">
						<Icon :name="DotsIcon" />
					</n-button>
				</n-dropdown>
			</div>
		</div>
	</n-card>
</template>

<script setup>
import { N<PERSON><PERSON><PERSON>, N<PERSON><PERSON><PERSON>, <PERSON>ard, NDescriptions, NDescriptionsItem, NDropdown } from "naive-ui"

defineProps({
	agenda: {
		type: Object,
		required: true
	}
})

const emits = defineEmits(["edit", "delete", "title-click"])

const viewport = useViewport()
const user = useDirectusUser()
const isAdmin = user.value.role.name === "Administrator"

const DotsIcon = "mage:dots"
const PencilIcon = "ion:pencil"
const TrashIcon = "ion:trash-outline"

const showDropdownRef = ref(false)

const options = ref([
	{
		label: "Edit",
		key: "event-edit",
		icon: renderIcon(PencilIcon)
	},
	{
		label: "Delete",
		key: "event-delete",
		icon: renderIcon(TrashIcon)
	}
])

const emitEdit = () => {
	emits("edit")
}

const emitDelete = () => {
	emits("delete")
}

const handleClick = () => {
	showDropdownRef.value = !showDropdownRef.value
}

const close = () => {
	showDropdownRef.value = false
}

const handleSelect = key => {
	if (key === "event-edit") {
		emitEdit()
	} else if (key === "event-delete") {
		emitDelete()
	}
}

const goto = () => {
	emits("title-click")
}
</script>

<style></style>
