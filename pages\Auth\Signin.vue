<template>
	<div class="page-auth">
		<!-- <Settings v-model:align="align" v-model:activeColor="activeColor"/> -->
		<div class="flex wrapper justify-center">
			<div class="image-box basis-2/3" v-if="align === 'right'"></div>
			<div class="form-box basis-1/3 flex items-center justify-center" :class="{ centered: align === 'center' }">
				<div class="form-wrap">
					<Logo mini :dark="isDark" class="mb-4" />
					<div class="title mb-4">Sign In</div>
					<div class="text mb-12">
						Today is a new day. It's your day. You shape it. Sign in to start managing your projects.
					</div>

					<div class="form">
						<transition name="form-fade" mode="out-in" appear>
							<n-form
								ref="formRef"
								:model="model"
								:rules="rules"
								@keydown.enter.prevent="handleValidateClick"
							>
								<n-form-item path="email" id="email" name="email" label="Email">
									<n-input
										v-model:value="model.email"
										placeholder="<EMAIL>"
										size="large"
										autocomplete="on"
									/>
								</n-form-item>
								<n-form-item path="password" id="password" name="password" label="Password">
									<n-input
										v-model:value="model.password"
										type="password"
										show-password-on="click"
										placeholder="At least 8 characters"
										autocomplete="on"
										size="large"
									/>
								</n-form-item>
								<div class="flex flex-col items-end gap-6">
									<div class="w-full">
										<n-button
											:loading="loading"
											type="primary"
											class="!w-full"
											size="large"
											@click="handleValidateClick"
										>
											Sign in
										</n-button>
									</div>
								</div>
							</n-form>
						</transition>
					</div>
				</div>
			</div>
			<div class="image-box basis-2/3" v-if="align === 'left'"></div>
		</div>
	</div>
</template>

<script setup>
import { NForm, NFormItem, NInput, NButton, useMessage } from "naive-ui"
// import Settings from "@/components/AuthForm/Settings.vue"
import { Layout } from "@/types/theme.d"
import Logo from "@/app-layouts/common/Logo.vue"

definePageMeta({
	name: "Signin",
	alias: "/signin",
	title: "Signin",
	forceLayout: Layout.Blank
})

const model = ref({
	email: "<EMAIL>",
	password: "password"
})

const rules = {
	email: [
		{
			required: true,
			trigger: ["blur"],
			message: "Email is required"
		}
	],
	password: [
		{
			required: true,
			trigger: ["blur"],
			message: "Password is required"
		}
	]
}

const { login, loading } = useLogin()

const align = ref("left")
const activeColor = ref("")
const themeStore = useThemeStore()
const isDark = computed(() => themeStore.isThemeDark)
const formRef = ref(null)
const message = useMessage()
const router = useRouter()
const onSubmit = async () => {
	try {
		await login(model.value.email, model.value.password)
		message.success("berhasil login")
		navigateTo("/events")
	} catch (e) {
		console.error(e.message)
		message.error("gagal login")
	} finally {
		loading.value = false
	}
}

const handleValidateClick = e => {
	e.preventDefault()
	formRef.value?.validate(errors => {
		if (!errors) {
			onSubmit()
		}
	})
}
</script>

<style lang="scss" scoped>
@import "./main.scss";

.page-auth {
	.wrapper {
		.image-box {
			background-color: v-bind(activeColor);
		}
	}
}

.form-wrap {
	width: 100%;
	min-width: 270px;
	max-width: 400px;

	.logo {
		:deep(img) {
			max-height: 37px;
		}
	}

	.title {
		font-size: 36px;
		font-family: var(--font-family-display);
		line-height: 1.2;
		font-weight: 700;
	}
	.text {
		font-size: 18px;
		line-height: 1.3;
		color: var(--fg-secondary-color);
	}

	.social-btns {
		.b-icon {
			margin-right: 16px;

			img {
				display: block;
				height: 20px;
			}
		}
	}
}

.form-fade-enter-active,
.form-fade-leave-active {
	transition:
		opacity 0.2s ease-in-out,
		transform 0.3s ease-in-out;
}
.form-fade-enter-from {
	opacity: 0;
	transform: translateX(10px);
}
.form-fade-leave-to {
	opacity: 0;
	transform: translateX(-10px);
}
</style>
