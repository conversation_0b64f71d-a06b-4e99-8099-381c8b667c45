<template>
	<n-card content-style="padding: 0px;">
		<template v-for="(guest, index) in guests" :key="guest.id">
			<div v-wave class="flex items-center p-4" @click="emitSelect(guest)">
				<n-flex vertical>
					<n-space vertical style="font-size: 12px">
						{{ guest.name }}
						<n-text depth="3">{{ guest.status_relation || "-" }}</n-text>
						<n-text depth="2">usher: {{ usher(guest) }}</n-text>
						<n-text depth="3">
							<data class="flex items-center">
								{{ giftsToString(guest.gift) }}
							</data>
						</n-text>
						<n-text depth="3" v-if="guest.entrust_by">
							<data class="flex items-center">dibawa oleh {{ guest.entrust_by }}</data>
						</n-text>
					</n-space>
				</n-flex>
			</div>
			<n-divider v-if="index < guests.length - 1" class="!my-2" />
		</template>
	</n-card>
</template>

<script setup>
import { NCard, N<PERSON>lex, NSpace, <PERSON><PERSON><PERSON><PERSON>, ND<PERSON>ider } from "naive-ui"
defineProps({
	guests: Array
})
const emits = defineEmits(["select"])
const emitSelect = guest => {
	emits("select", guest)
}

const usher = guest => {
	let usher = "-"
	if (guest.regist_by) {
		if (guest.regist_by.first_name) {
			usher = `${guest.regist_by.first_name}`
		}
		if (guest.regist_by.last_name) {
			usher += ` ${guest.regist_by.last_name}`
		}
	}
	return usher
}
</script>

<style></style>
