<template>
  <div>
    <n-space vertical>
      <h2>Camera Input Example</h2>
      
      <!-- Basic usage -->
      <div>
        <h3>Basic Usage</h3>
        <CameraInput @capture="onCapture" />
      </div>
      
      <!-- Custom trigger button -->
      <div>
        <h3>Custom Trigger Button</h3>
        <CameraInput @capture="onCapture" button-text="Take a selfie">
          <template #trigger="{ openCamera }">
            <n-button @click="openCamera" type="success">
              <template #icon>
                <Icon name="carbon:camera" :size="20" />
              </template>
              Custom Camera Button
            </n-button>
          </template>
        </CameraInput>
      </div>
      
      <!-- Display captured image -->
      <div v-if="capturedImage">
        <h3>Captured Image</h3>
        <n-card>
          <img :src="capturedImage" style="max-width: 100%; max-height: 300px;" />
          <template #footer>
            <n-button @click="capturedImage = null" type="error">Clear</n-button>
          </template>
        </n-card>
      </div>
    </n-space>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { NSpace, NButton, NCard } from 'naive-ui'
import { Icon } from '@iconify/vue'
import CameraInput from '../common/CameraInput.vue'

const capturedImage = ref(null)

const onCapture = (imageData) => {
  capturedImage.value = imageData
  console.log('Image captured:', imageData.substring(0, 50) + '...')
}
</script>
