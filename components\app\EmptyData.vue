import { NEmpty, NButton } from 'naive-ui';
<template>
	<n-empty class="my-10" size="large" description="data kosong">
		<template #extra>
			<n-button size="small" @click="emitRefresh">refresh</n-button>
		</template>
	</n-empty>
</template>

<script setup>
import { NEmpty, NButton } from "naive-ui"
const emit = defineEmits(["refresh"])
const emitRefresh = () => {
	emit("refresh")
}
</script>

<style></style>
