export default defineEventHandler(async event => {
	// Parse the incoming query parameters
	const query = getQuery(event)

	// Example parameters from query object
	const { name = "<PERSON><PERSON> & Anshar" } = query

	// Create the response object
	const response = {
		1: {
			type: 0,
			content: "--------------------------------",
			bold: 0,
			align: 1,
			format: 0
		},
		2: {
			type: 0,
			content: name,
			bold: 1,
			align: 1,
			format: 0
		},
		3: {
			type: 0,
			content: "--------------------------------",
			bold: 0,
			align: 1,
			format: 0
		},
		4: {
			type: 0,
			content: " ",
			bold: 0,
			align: 0,
			format: 4
		},
		5: {
			type: 3,
			value: name,
			size: 30,
			align: 1
		},
		6: {
			type: 0,
			content: " ",
			bold: 0,
			align: 0,
			format: 4
		},
		7: {
			type: 0,
			content: "*Struk ini digunakan sebagai voucher pengambilan souvenir*",
			bold: 0,
			align: 0,
			format: 4
		},
		8: {
			type: 0,
			content: "********************************",
			bold: 0,
			align: 1,
			format: 0
		},
		9: {
			type: 0,
			content: " ",
			bold: 0,
			align: 0,
			format: 4
		}
	}

	// Return the response
	return response
})
