// import { createDirectus } from "@directus/sdk"
// export default defineNuxtRouteMiddleware(async (to, _from) => {
// 	const { fetchUser, setUser } = useDirectusAuth()
// 	const user = useDirectusUser()
// 	if (!user.value) {
// 		const user = await fetchUser()
// 		console.log("user", user)
// 		setUser(user.value)
// 	}
// 	if (!user.value && to.path == "/auth/signin") {
// 		return navigateTo("/auth/signin")
// 	}
// })

export default defineNuxtRouteMiddleware(async (to, _from) => {
	const { token_expired, refreshTokens } = useDirectusToken()
	// @ts-ignore
	// const { $directus } = useNuxtApp()
	// Check if the token's expired
	if (token_expired.value) {
		try {
			// Attempt to refresh the token
			await refreshTokens()
			// const { token } = useDirectusToken()
			// @ts-ignore
			// await $directus.setToken(token.value)
			// If still expired, off to the login page
			if (token_expired.value && to.path != "/auth/signin") {
				return navigateTo("/auth/signin")
			}
		} catch (error) {
			// Handle any refresh errors here
			console.error("Couldn't refresh token:", error)
		}
	}
	// If the token's good, carry on
})
