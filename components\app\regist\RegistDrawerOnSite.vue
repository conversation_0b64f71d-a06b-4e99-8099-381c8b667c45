<template>
	<CustomDrawer v-model:show="show" title="Tamu On Site" @close="handleClose">
		<n-form ref="formGuestRef" :rules="rules" :model="guestModel">
			<n-form-item label="Nama tamu" path="name">
				<n-input
					v-model:value="guestModel.name"
					clearable
					style="width: 100%"
					placeholder="Masukkan nama tamu"
				/>
			</n-form-item>
			<n-form-item label="Relasi/Status" path="status_relation">
				<n-input
					v-model:value="guestModel.status_relation"
					clearable
					style="width: 100%"
					placeholder="Masukkan nama tamu"
				/>
			</n-form-item>
			<n-form-item label="tipe tamu" path="level">
				<n-radio-group v-model:value="guestModel.level" name="level">
					<n-space>
						<n-radio
							v-for="item in optionsLevel"
							:key="item.value"
							:value="item.value"
							:label="item.label"
						/>
					</n-space>
				</n-radio-group>
			</n-form-item>
			<n-form-item label="Jumlah tamu" path="amount_guest">
				<n-input-number
					v-model:value="guestModel.amount_guest"
					clearable
					:min="0"
					style="width: 100%"
					placeholder="Masukkan jumlah tamu"
				/>
			</n-form-item>
			<n-form-item path="gift" label="Hadiah">
				<n-checkbox-group v-model:value="guestModel.gift">
					<n-space item-style="display: flex;">
						<n-checkbox value="1" label="Cash" />
						<n-checkbox value="2" label="Kado" />
						<n-checkbox value="3" label="Transfer" />
					</n-space>
				</n-checkbox-group>
			</n-form-item>
			<n-form-item label="Keterangan" path="label">
				<n-input
					v-model:value="guestModel.label"
					type="text"
					clearable
					placeholder="Masukkan Keterangan (opsional)"
				/>
			</n-form-item>
			<n-form-item label="Jumlah souvenir" path="amount_souvenir">
				<n-input-number
					v-model:value="guestModel.amount_souvenir"
					clearable
					:min="0"
					style="width: 100%"
					placeholder="Masukkan jumlah souvenir"
				/>
			</n-form-item>
			<n-form-item label="Souvenir diberikan" path="take_souvenir">
				<n-switch v-model:value="guestModel.take_souvenir" />
			</n-form-item>

			<n-form-item v-if="agenda?.photo">
				<SelfieInput
					v-model="guestModel.selfieInput"
					camera-title="Selfie"
					camera-button-text="Ambil Selfie"
					aspect-ratio="1/1"
				/>
			</n-form-item>

			<template v-for="(item, index) in entrustModel.entrusts" :key="'titipan-' + index">
				<n-card class="mb-3" :title="'Titipan ' + (index + 1)" closable @close="removeItem(index)">
					<n-form-item ignore-path-change label="Nama" :path="`entrusts.${index}.name`">
						<n-input v-model:value="item.name" placeholder="Masukkan nama tamu" @keydown.enter.prevent />
					</n-form-item>
					<n-form-item ignore-path-change label="Hadiah" :path="`entrusts.${index}.gift`">
						<n-checkbox-group v-model:value="item.gift">
							<n-space item-style="display: flex;">
								<n-checkbox value="1" label="Cash" />
								<n-checkbox value="2" label="Kado" />
								<n-checkbox value="3" label="Transfer" />
							</n-space>
						</n-checkbox-group>
					</n-form-item>
					<n-form-item label="Souvenir diberikan">
						<n-switch v-model:value="item.take_souvenir" />
					</n-form-item>
					<n-form-item v-if="agenda?.print_feature && !isIos">
						<n-button class="w-full mb-4" @click="printEntrust(index)">
							<template #icon>
								<Icon name="carbon:printer" />
							</template>
							Cetak
						</n-button>
					</n-form-item>
				</n-card>
			</template>

			<n-form-item>
				<n-button block @click="addEntrust">
					<template #icon>
						<Icon name="carbon:gift" />
					</template>
					Tambah titipan
				</n-button>
			</n-form-item>
			<n-button v-if="agenda?.print_feature && !isIos" class="w-full mb-4" @click="printContent">
				<template #icon>
					<Icon name="carbon:printer" />
				</template>
				Cetak
			</n-button>
		</n-form>
		<template #footer>
			<n-button block :loading="submitLoading" type="primary" @click="handleSubmit">Konfirmasi</n-button>
		</template>
	</CustomDrawer>
</template>

<script setup>
import {
	NCard,
	NForm,
	NFormItem,
	NInputNumber,
	NCheckboxGroup,
	NSpace,
	NRadioGroup,
	NRadio,
	NCheckbox,
	NInput,
	NSwitch,
	NButton,
	useLoadingBar,
	useMessage,
	useNotification
} from "naive-ui"
import CustomDrawer from "@/components/app/CustomDrawer.vue"
import ShortUniqueId from "short-unique-id"

const props = defineProps({
	agenda: Object
})

const emit = defineEmits(["close", "after-submit", "print-content", "print-entrust"])

const show = defineModel()
const { randomUUID } = new ShortUniqueId({ length: 4 })

const loadingBar = useLoadingBar()
const notification = useNotification()
const { createItems } = useDirectusItems()
const user = useDirectusUser()
const selectedPubChannel = useSelectedPubChannel()
const ably = useNuxtApp().$ably
const { isIos } = useDevice()
const config = useRuntimeConfig()
const { token } = useDirectusToken()

const guestModel = ref({
	name: null,
	status_relation: null,
	level: "1",
	amount_guest: 0,
	amount_souvenir: 0,
	take_souvenir: false,
	gift: [],
	label: null,
	on_Site: true,
	presence: true,
	event: props.agenda.id,
	regist_by: user.value.id,
	selfie: null,
	selfieInput: null
})

const entrustModel = reactive({
	entrusts: []
})

const sendMessage = message => {
	let channelName = `${props.agenda.id}`
	if (selectedPubChannel.value != "global") {
		channelName = `${props.agenda.id}-${user.value.id}`
	}
	const channel = ably.channels.get(channelName)
	channel.publish("greeting", message)
}

const resetModel = () => {
	guestModel.value = {
		name: null,
		status_relation: null,
		level: "1",
		amount_guest: 0,
		amount_souvenir: 0,
		take_souvenir: false,
		gift: [],
		label: null,
		on_Site: true,
		presence: true,
		event: props.agenda.id,
		regist_by: user.value.id,
		selfie: null,
		selfieInput: null
	}
}

const resetEntrust = () => {
	entrustModel.entrusts = []
}

const addEntrust = () => {
	entrustModel.entrusts.push({
		name: null,
		code_guest: null,
		gift: [],
		presence: false,
		amount_guest: 1,
		take_souvenir: false,
		amount_souvenir: 1,
		entrust: true,
		event: "props.agenda.id",
		regist_by: user.value.id
	})
}

const removeItem = index => {
	entrustModel.entrusts.splice(index, 1)
}

const formGuestRef = ref(null)
const rules = {
	name: {
		required: true,
		message: "Nama tidak boleh kosong",
		trigger: ["input", "blur"]
	}
}

const submitLoading = ref(false)

const handleSubmit = e => {
	e.preventDefault()
	formGuestRef.value?.validate(async errors => {
		if (!errors) {
			await submitGuest()
		} else {
			console.error(errors)
		}
	})
}

function safeFileName(name) {
	return name
		.toLowerCase()
		.replace(/[^\w\s.-]/g, "") // remove any character except word characters, spaces, dots, and dashes
		.replace(/\s+/g, "_") // replace spaces with underscores
		.replace(/_+/g, "_") // collapse multiple underscores
		.replace(/^_+|_+$/g, "") // remove underscores at start/end
}

const submitGuest = async () => {
	submitLoading.value = true
	loadingBar.start()
	try {
		guestModel.value.code_guest = props.agenda.code_event + "-site-" + randomUUID()

		if (props.agenda.photo && guestModel.value.selfieInput) {
			const nameSafe = safeFileName(guestModel.value.name)
			const base64Response = await fetch(guestModel.value.selfieInput)
			const blob = await base64Response.blob()
			// Upload the file and assign it to the folder
			const formData = new FormData()
			formData.append("folder", "4327512e-0b84-4b2f-b844-1f5929e8b9d9") // Assign file to folder
			formData.append("file", blob, `${nameSafe}--${guestModel.value.code_guest}.jpeg`)

			const result = await $fetch(`/files`, {
				baseURL: config.public.directusUrl,
				method: "POST",
				headers: {
					Authorization: `Bearer ${token.value}`
				},
				body: formData
			})
			guestModel.value.selfie = result.data.id
		}

		// console.log("guestModel.value", guestModel.value)
		await createItems({ collection: "guest", items: guestModel.value })
		if (entrustModel.entrusts.length > 0) {
			entrustModel.entrusts.forEach((item, index) => {
				if (!item.name) {
					entrustModel.entrusts.splice(index, 1)
				}
			})
			entrustModel.entrusts.forEach(item => {
				item.event = props.agenda.id
				item.code_guest = props.agenda.code_event + "-gift-" + randomUUID()
			})
		}
		// console.log("entrustModel.entrusts", entrustModel.entrusts)
		const guestMessage = {
			// id: props.selectedGuest.id,
			name: guestModel.value.name,
			status_relation: guestModel.value.status_relation,
			table: "",
			address: "",
			amount_guest: guestModel.value.amount_guest,
			level: guestModel.value.level
		}
		if (props.agenda.greeting_screen) {
			sendMessage(guestMessage)
		}
		await createItems({ collection: "guest", items: entrustModel.entrusts })
		emit("after-submit")
		loadingBar.finish()
		notification.success({
			content: `${guestModel.value.name} dinyatakan hadir on-site`,
			description: `Sukses`,
			duration: 2000
		})
		resetModel()
		resetEntrust()
	} catch (e) {
		console.error(e)
		if (e.errors) {
			notification.error({
				content: `${e.errors[0].message}`,
				description: `Error`,
				duration: 2000
			})
		} else {
			notification.error({
				content: `gagal konfirmasi`,
				description: `Error`,
				duration: 2000
			})
		}
		loadingBar.error()
	} finally {
		submitLoading.value = false
	}
}

const linkPrintParam = (title, name) => {
	if (!title || !name) return
	const _title = encodeURIComponent(title)
	const _name = encodeURIComponent(name)
	const _qrvalue = encodeURIComponent(`on-site-${title}`)

	return `my.bluetoothprint.scheme://https://app.bagimomen.my.id/api/print-guest?name=${_name}&title=${_title}&qrvalue=${_qrvalue}`
}

const printContent = async () => {
	const guestPrint = {
		...props.selectedGuest,
		...guestModel.value,
		code_guest: "on-site-" + "belum-ditentukan"
	}
	emit("print-content", guestPrint)
}

const printEntrust = index => {
	emit("print-entrust", entrustModel.entrusts[index])
}

const handleClose = () => {
	emit("close")
}
</script>

<style lang="scss" scoped>
:deep(.n-input__suffix) {
	gap: 8px;
}
</style>
