import { NTag, <PERSON><PERSON>utton, NS<PERSON>, <PERSON><PERSON><PERSON>, NP } from "naive-ui"
import Icon from "~/components/common/Icon.vue"
import { type Guest } from "~/types/globals"
export const useGuestsColumnDataTables = (handleEdit: (row: Guest) => void, handleDelete: (row: Guest) => void) => {
	const EditIcon = "la:edit"
	const DeleteIcon = "la:trash-alt"
	const rowKey = (row: Guest) => row.id

	const desktopColumns = [
		{
			type: "selection",
			fixed: "left"
		},
		{
			title: "Nama",
			key: "name",
			resizable: true,
			minWidth: 200
		},
		{
			title: "Status",
			key: "status_relation",
			resizable: true,
			minWidth: 150
		},
		{
			title: "Kode",
			key: "code_guest",
			resizable: true,
			minWidth: 80
		},
		{
			title: "Level",
			key: "level",
			resizable: true,
			minWidth: 80,
			render(row: Guest) {
				return h(
					NTag,
					{
						type: (row.level ?? 0) > 1 ? "warning" : "default",
						round: true,
						class: row.level == 3 ? "gloss" : ""
					},
					{ default: () => translateLevel(row.level ?? 0) }
				)
			}
		},
		{
			title: "Sesi",
			key: "shift",
			resizable: true,
			minWidth: 100
		},
		{
			title: "Meja",
			key: "table",
			resizable: true,
			minWidth: 100
		},
		{
			title: "Kategori",
			key: "category",
			resizable: true,
			minWidth: 150
		},
		{
			title: "Alamat",
			key: "address",
			resizable: true,
			minWidth: 150
		},
		{
			title: "No HP",
			key: "phone",
			resizable: true,
			minWidth: 150
		},
		{
			title: "Pax",
			key: "amount_guest",
			resizable: true,
			minWidth: 80
		},
		{
			title: "Souvenir",
			key: "amount_souvenir",
			resizable: true,
			minWidth: 80
		},
		{
			title: "Catatan",
			key: "note",
			resizable: true,
			minWidth: 100
		},
		{
			title: "RSVP",
			key: "rsvp",
			resizable: true,
			minWidth: 80,
			render(row: Guest) {
				return h(
					"span",
					{},
					{
						default: () => (row.rsvp ? (row.rsvp == "0" ? "Not going" : "Going") : "")
					}
				)
			}
		},
		{
			title: "Aksi",
			key: "actions",
			minWidth: 80,
			render(row: Guest) {
				return h(
					NSpace,
					{
						size: "small"
					},
					{
						default: () => [
							h(
								NButton,
								{
									size: "small",
									circle: true,
									type: "warning",
									onClick: () => handleEdit(row)
								},
								{
									icon: () =>
										h(Icon, {
											name: EditIcon
										})
								}
							),
							h(
								NButton,
								{
									size: "small",
									circle: true,
									type: "error",
									onClick: () => handleDelete(row)
								},
								{
									icon: () =>
										h(Icon, {
											name: DeleteIcon
										})
								}
							)
						]
					}
				)
			}
		}
	]

	const mobileColumns = [
		{
			type: "selection",
			fixed: "left"
		},
		{
			title: "Daftar Tamu",
			elipsis: true,
			render(row: Guest) {
				return h("div", [
					h(
						NEllipsis,
						{},
						{
							default: () => [
								h(
									NP,
									{
										class: ["mt-0 mb-0 text-xs"],
										depth: 1
									},
									{ default: () => row.name }
								),
								h(
									NP,
									{
										class: ["mt-0 mb-0 text-xs"],
										depth: 3
									},
									{ default: () => row.status_relation }
								)
							]
						}
					)
				])
			}
		},
		{
			title: "",
			key: "level",
			minWidth: 80, // Keep fixed width for level
			render(row: Guest) {
				return h(
					NTag,
					{
						type: (row.level ?? 0) > 1 ? "warning" : "default",
						round: true,
						class: "text-xs",
						size: "small"
					},
					{ default: () => translateLevel(row.level ?? 0) }
				)
			}
		},
		{
			title: "RSVP",
			key: "rsvp",
			resizable: true,
			minWidth: 80,
			render(row: Guest) {
				return h(
					"span",
					{},
					{
						default: () => (row.rsvp ? (row.rsvp == "0" ? "Not going" : "Going") : "")
					}
				)
			}
		},

		{
			title() {
				return null
			},
			key: "actions",
			width: 70, // Keep fixed width for actions
			render(row: Guest) {
				return h(
					NSpace,
					{
						size: "small"
					},
					{
						default: () => [
							h(
								NButton,
								{
									size: "small",
									text: true,
									type: "warning",
									onClick: () => handleEdit(row)
								},
								{
									icon: () =>
										h(Icon, {
											name: EditIcon
										})
								}
							),
							h(
								NButton,
								{
									size: "small",
									text: true,
									type: "error",
									onClick: () => handleDelete(row)
								},
								{
									icon: () =>
										h(Icon, {
											name: DeleteIcon
										})
								}
							)
						]
					}
				)
			}
		}
	]

	return { desktopColumns, mobileColumns, rowKey }
}
