<template>
	<div class="page">
		<n-h1 prefix="bar" class="mb-4">
			<h1>Cetak</h1>
		</n-h1>
		<n-tabs v-if="agendaData" type="segment" animated>
			<n-tab-pane name="template" tab="template">
				<n-card :title="`template ${activeTemplate}`">
					<n-tabs v-model:value="activeTemplate" type="line" animated>
						<n-tab-pane name="umum" tab="umum">
							<n-dynamic-input
								v-model:value="templateGeneral"
								item-class="border border-[--primary-color] p-3 rounded-lg border-dashed"
								:on-create="onCreate"
							>
								<template #create-button-default>Tambah Konten</template>
								<template #default="{ value }">
									<div
										v-if="!viewport.isLessThan('tablet')"
										style="display: flex; align-items: center; width: 100%"
									>
										<n-select
											v-model:value="value.align"
											:options="optionsAlignment"
											style="min-width: 120px; width: 120px; margin-right: 12px"
										/>
										<n-select
											v-model:value="value.type"
											:options="optionsType"
											style="min-width: 100px; width: 100px; margin-right: 12px"
										/>

										<n-input
											v-model:value="value.content"
											type="text"
											placeholder="Masukkan konten"
										/>
									</div>

									<n-grid v-else :cols="2" :x-gap="8" :y-gap="8">
										<n-gi :span="1">
											<n-select
												v-model:value="value.align"
												:options="optionsAlignment"
												style="margin-right: 12px"
											/>
										</n-gi>
										<n-gi :span="1">
											<n-select
												v-model:value="value.type"
												:options="optionsType"
												style="margin-right: 12px"
											/>
										</n-gi>
										<n-gi :span="2">
											<n-input
												v-model:value="value.content"
												type="text"
												placeholder="Masukkan konten"
											/>
										</n-gi>
									</n-grid>
								</template>
								<template #action="{ index, create, remove }">
									<n-space v-if="viewport.isLessThan('tablet')" justify="end">
										<n-button @click="() => create(index)">
											<Icon name="icon-park-outline:add" :size="16" />
										</n-button>
										<n-button @click="() => remove(index)">
											<Icon name="icon-park-outline:delete" :size="16" />
										</n-button>
									</n-space>
									<n-button-group v-else style="margin-left: 16px">
										<n-button @click="() => remove(index)">
											<Icon name="icon-park-outline:delete" :size="18" />
										</n-button>
										<n-button @click="() => create(index)">
											<Icon name="icon-park-outline:add" :size="18" />
										</n-button>
									</n-button-group>
								</template>
							</n-dynamic-input>
							<n-space
								:vertical="viewport.isLessThan('tablet')"
								:justify="!viewport.isLessThan('tablet') ? 'end' : ''"
								class="mt-4"
								size="large"
							>
								<n-button
									:loading="loadingSave"
									:style="viewport.isLessThan('tablet') && 'width: 100%'"
									@click="saveTemplate"
								>
									<template #icon>
										<Icon name="material-symbols:save-outline" />
									</template>
									Simpan Template
								</n-button>
								<n-button :style="viewport.isLessThan('tablet') && 'width: 100%'" @click="connectPrint">
									<template #icon>
										<Icon name="clarity:connect-line" />
									</template>
									connect printer
								</n-button>
								<n-button
									v-if="agendaData.print_feature"
									:style="viewport.isLessThan('tablet') && 'width: 100%'"
									@click="printContent2"
								>
									<template #icon>
										<Icon name="carbon:printer" />
									</template>
									cetak
								</n-button>
							</n-space>
						</n-tab-pane>
						<n-tab-pane name="tamu" tab="tamu">
							<n-dynamic-input
								v-model:value="templateGuest"
								item-class="border border-[--primary-color] p-3 rounded-lg border-dashed"
								:on-create="onCreate"
							>
								<template #create-button-default>Tambah Konten</template>
								<template #default="{ value }">
									<div
										v-if="!viewport.isLessThan('tablet')"
										style="display: flex; align-items: center; width: 100%"
									>
										<n-select
											v-model:value="value.align"
											:options="optionsAlignment"
											style="min-width: 120px; width: 120px; margin-right: 12px"
										/>
										<n-select
											v-model:value="value.type"
											:options="optionsType"
											style="min-width: 100px; width: 100px; margin-right: 12px"
										/>

										<n-input
											v-model:value="value.content"
											type="text"
											placeholder="Masukkan konten"
										/>
									</div>

									<n-grid v-else :cols="2" :x-gap="8" :y-gap="8">
										<n-gi :span="1">
											<n-select
												v-model:value="value.align"
												:options="optionsAlignment"
												style="margin-right: 12px"
											/>
										</n-gi>
										<n-gi :span="1">
											<n-select
												v-model:value="value.type"
												:options="optionsType"
												style="margin-right: 12px"
											/>
										</n-gi>
										<n-gi :span="2">
											<n-input
												v-model:value="value.content"
												type="text"
												placeholder="Masukkan konten"
											/>
										</n-gi>
									</n-grid>
								</template>
								<template #action="{ index, create, remove }">
									<n-space v-if="viewport.isLessThan('tablet')" justify="end">
										<n-button @click="() => create(index)">
											<Icon name="icon-park-outline:add" :size="16" />
										</n-button>
										<n-button @click="() => remove(index)">
											<Icon name="icon-park-outline:delete" :size="16" />
										</n-button>
									</n-space>
									<n-button-group v-else style="margin-left: 16px">
										<n-button @click="() => remove(index)">
											<Icon name="icon-park-outline:delete" :size="18" />
										</n-button>
										<n-button @click="() => create(index)">
											<Icon name="icon-park-outline:add" :size="18" />
										</n-button>
									</n-button-group>
								</template>
							</n-dynamic-input>
							<n-space
								:vertical="viewport.isLessThan('tablet')"
								:justify="!viewport.isLessThan('tablet') ? 'end' : ''"
								class="mt-4"
								size="large"
							>
								<n-button
									:loading="loadingSave"
									:style="viewport.isLessThan('tablet') && 'width: 100%'"
									@click="saveTemplate"
								>
									<template #icon>
										<Icon name="material-symbols:save-outline" />
									</template>
									Simpan Template
								</n-button>
							</n-space>
						</n-tab-pane>
					</n-tabs>
				</n-card>
			</n-tab-pane>
			<n-tab-pane name="penggunaan" tab="Penggunaan">
				<n-steps vertical>
					<n-step title="Koneksi Bluetooth">
						aktifkan bluetooth dan hubungkan dengan printer (masukkkan kode 0000 atau 1234 jika diminta)
					</n-step>
					<n-step title="Hubungkan Printer">
						klik tombol connect (
						<Icon name="clarity:connect-line" />
						), lalu piih printer. beri perizinan jika diperlukan
					</n-step>
					<n-step title="Perizinan Lanjutan">
						(opsional) jika menggunakan browser berbasis chromium, masukkan url chrome://flags/
						<br />
						enable fitur "Use the new permissions backend for Web Bluetooth" dan "Web Bluetooth confirm
						pairing support" (jika ada)
					</n-step>
					<n-step title="Hubungkan Kembali">Hubungkan kembali printer jika melakukan refresh</n-step>
				</n-steps>
			</n-tab-pane>
			<n-tab-pane name="panduan" tab="Panduan">
				<n-card title="Panduan template">
					<n-image
						class="border border-[--primary-color] p-3 rounded-lg border-dashed"
						src="/images/panduan-template-cetak.jpg"
						width="100%"
					/>

					<n-alert class="mt-8" title="Kata kunci" type="info">
						[event] = judul event
						<br />
						[tanggal] = tanggal event
						<br />
						[lokasi] = lokasi event
						<br />
						<br />
						=====khusus untuk template tamu=====
						<br />
						<br />
						[kode-tamu] = kode tamu
						<br />
						[nama-tamu] = nama tamu
						<br />
						[status] = status relasi tamu
						<br />
						[level] = level tamu
						<br />
						[meja] = meja tamu
						<br />
						[pax] = pax tamu
					</n-alert>
				</n-card>
			</n-tab-pane>
		</n-tabs>

		<!-- <n-button class="" tag="a" :href="linkPrint">
			<template #icon>
				<Icon name="carbon:printer" />
			</template>
			cetak voucher
		</n-button> -->
	</div>
</template>

<script setup>
import {
	NSpace,
	NAlert,
	NButton,
	NSteps,
	NStep,
	NA,
	NUl,
	NLi,
	NInput,
	useMessage,
	NTabs,
	NTabPane,
	NDynamicInput,
	NGrid,
	NGi,
	NButtonGroup,
	NImage,
	NCard
} from "naive-ui"
import WebBluetoothReceiptPrinter from "~/webbluetooth-receipt-printer.esm"
import ReceiptPrinterEncoder from "@point-of-sale/receipt-printer-encoder"
import ReceiptPrinterStatus from "~/receipt-printer-status.esm.js"

definePageMeta({
	name: "Print",
	title: "Print"
})

const route = useRoute()
const { getItemById } = useDirectusItems()
const agendaData = ref(null)
const message = useMessage()
const viewport = useViewport()

const receiptPrinter = new WebBluetoothReceiptPrinter()
const lastPrinter = useLastPrinter()
let encoder = new ReceiptPrinterEncoder({ columns: 32 })
let printerStatus = null

const activeTemplate = ref("umum")

const optionsAlignment = [
	{ label: "Kiri", value: "left" },
	{ label: "Tengah", value: "center" },
	{ label: "Kanan", value: "right" }
]

const optionsType = [
	{ label: "Judul", value: "size" },
	{ label: "Teks", value: "text" },
	{ label: "Bold", value: "bold" },
	{ label: "Italic", value: "italic" },
	{ label: "QR", value: "qrcode" }
]

const templateGeneral = ref(defaultTemplateGeneral)

const templateGuest = ref(defaultTemplateGuest)

const onCreate = () => {
	return {
		align: "left",
		type: "text",
		content: ""
	}
}

receiptPrinter.addEventListener("connected", device => {
	console.log(`Connected to ${device.name} (#${device.id})`)

	encoder = new ReceiptPrinterEncoder({
		language: device.language,
		columns: 32
	})
	/* Store device for reconnecting */
	lastPrinter.value = device

	printerStatus = new ReceiptPrinterStatus({
		printer: receiptPrinter,
		language: device.language
	})

	printerStatus.addEventListener("connected", () => {
		message.success(`status: Connected to ${device.name}`)
	})

	printerStatus.addEventListener("unsupported", () => {
		message.error(`status: Unsupported printer ${device.name}`)
	})

	printerStatus.addEventListener("disconnected", () => {
		message.info(`status: Disconnected from ${device.name}`)
	})
})

receiptPrinter.addEventListener("disconnected", () => {
	console.log(`printer disconnected`)
})

const connectPrint = async () => {
	await receiptPrinter.connect()
}

const reconnectPrint = async () => {
	if (lastPrinter.value) {
		await receiptPrinter.reconnect(lastPrinter.value)
		await new Promise(resolve => setTimeout(resolve, 500))
	}
}

const replaceKeyword = text => {
	const resultText = text
	return resultText
		.replaceAll("[event]", agendaData.value.title || "")
		.replaceAll("[tanggal]", agendaData.value.start || "")
		.replaceAll("[lokasi]", agendaData.value.location || "")
}

const transformInputToEncoder = inputArray => {
	inputArray.forEach(command => {
		switch (command.align) {
			case "left":
				encoder.align("left")
				break
			case "center":
				encoder.align("center")
				break
			case "right":
				encoder.align("right")
				break
			default:
				console.warn(`Unknown command type: ${command.align}`)
		}

		switch (command.type) {
			case "size":
				encoder.size(2).line(replaceKeyword(command.content)).size(1)
				break
			case "text":
				encoder.line(replaceKeyword(command.content))
				break
			case "bold":
				encoder.bold(true).line(replaceKeyword(command.content)).bold(false)
				break
			case "italic":
				encoder.italic(true).line(replaceKeyword(command.content)).italic(false)
				break
			case "qrcode":
				encoder.qrcode(replaceKeyword(command.content))
				break
			default:
				console.warn(`Unknown command type: ${command.align}`)
		}
	})

	encoder.newline(3)
	return encoder.encode() // Generate the final ESC/POS commands
}

// Print Receipt via RAWBT
const printContent2 = async () => {
	try {
		if (!printerStatus || !printerStatus.connected) {
			await reconnectPrint()
		}

		if (printerStatus && printerStatus.connected) {
			// Format content using the encoder
			const result = transformInputToEncoder(templateGeneral.value)

			receiptPrinter.print(result)
		} else {
			message.error("Printer belum terhubung")
		}
	} catch (error) {
		message.error("Gagal mencetak")
	}
}

const { createItems, updateItem } = useDirectusItems()
const loadingSave = ref(false)
const saveTemplate = async () => {
	loadingSave.value = true
	try {
		let printData = null
		if (agendaData.value && agendaData.value.print_template) printData = agendaData.value.print_template

		const field =
			activeTemplate.value === "umum"
				? { template_general: JSON.stringify(templateGeneral.value) }
				: { template_guest: JSON.stringify(templateGuest.value) }

		if (!printData) {
			const result = await createItems({
				collection: "print",
				items: field
			})
			console.log(result)
			const result2 = await updateItem({
				id: agendaData.value.id,
				collection: "event",
				item: { print_template: result.id }
			})
			agendaData.value = result2
		} else {
			await updateItem({
				id: printData.id,
				collection: "print",
				item: field
			})
		}

		message.success("Template berhasil disimpan")
	} catch (e) {
		message.error("Template gagal disimpan")
	} finally {
		loadingSave.value = false
	}
}

const fetchAgenda = async () => {
	try {
		const item = await getItemById({
			collection: "event",
			id: route.params.id,
			params: {
				fields: [
					"id",
					"title",
					"greeting_background",
					"print_feature",
					"print_template.*",
					"users.directus_users_id",
					"users.directus_users_id.id.print_template",
					"users.directus_users_id.id.print_guest",
					"users.directus_users_id.first_name",
					"users.directus_users_id.last_name"
				]
			}
		})
		agendaData.value = item
	} catch (e) {
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
		} else {
			console.error(e)
		}
	}
}

onMounted(async () => {
	await fetchAgenda()
	if (agendaData.value && agendaData.value.print_template) {
		if (agendaData.value.print_template.template_general)
			templateGeneral.value = JSON.parse(agendaData.value.print_template.template_general)
		if (agendaData.value.print_template.template_guest)
			templateGuest.value = JSON.parse(agendaData.value.print_template.template_guest)
	}

	if (lastPrinter.value) {
		await reconnectPrint()
	}
})
</script>

<style></style>
