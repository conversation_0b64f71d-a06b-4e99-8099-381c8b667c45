export const useLogin = () => {
	const { login } = useDirectusAuth()
	const { $directus } = useNuxtApp()
	const loading = ref(false)

	const loginWithDirectus = async (email: string, password: string) => {
		loading.value = true
		await login({ email, password })
		const { token } = useDirectusToken()
		await $directus.setToken(token.value)
		loading.value = false
	}

	return { login: loginWithDirectus, loading }
}
