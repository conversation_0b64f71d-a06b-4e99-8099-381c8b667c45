<template>
	<div class="page">
		<n-h1 prefix="bar" class="mb-0">
			<h1>
				MANAJEMEN TAMU
				<n-button circle type="secondary" :loading="reloadLoading" @click="refresh">
					<template #icon>
						<Icon :name="ReloadIcon" />
					</template>
				</n-button>
			</h1>
		</n-h1>
		<n-text v-if="guestAggregated" class="ml-5" depth="3">
			{{ guestAggregated.guest_aggregated[0].count.id }} undangan dan
			{{ guestAggregated.guest_aggregated[0].sum.amount_guest }} pax ditemukan
		</n-text>

		<template v-if="guestsData">
			<n-space vertical size="large" class="mt-5">
				<n-space>
					<n-card id="stats-card" v-if="rsvpAggregated" class="card">
						<template #header>
							<n-flex align="center" size="small">
								<Icon name="oui:stats" :size="28" />
								<span>Statistik RSVP</span>
							</n-flex>
						</template>
						<n-space vertical>
							<n-text>
								<Icon name="oui:dot" color="#7bd557" />
								{{ going }} undangan | {{ sumGoing }} pax berencana hadir
							</n-text>
							<n-text>
								<Icon name="oui:dot" color="#f9226a" />
								{{ notGoing }} undangan tidak hadir
							</n-text>
							<n-text>
								<Icon name="oui:dot" color="#b5b8be" />
								{{ unconfirm }} undangan | {{ sumUnconfirm }} pax belum konfirmasi
							</n-text>
						</n-space>
					</n-card>
				</n-space>
				<n-space align="center" justify="space-between">
					<n-space align="center">
						<n-input
							id="search-input"
							v-model:value="fetchParam.search"
							placeholder="Cari Tamu"
							clearable
							@keyup.enter="fetchGuests"
							@input="delayedSearch"
							@clear="handleClear"
						/>
					</n-space>
					<n-space
						id="action-buttons"
						align="center"
						:size="viewport.isLessThan('tablet') ? 'small' : 'medium'"
					>
						<n-button circle type="primary" :ghost="!showOptions" @click="showOptions = !showOptions">
							<template #icon>
								<Icon :name="SettingIcon" />
							</template>
						</n-button>

						<n-upload
							v-if="isAdmin"
							accept=".xlsx, .csv"
							list-type="picture-card"
							:show-file-list="false"
							@change="handleFileChange"
						>
							<n-button
								:circle="viewport.isLessThan('desktop')"
								type="primary"
								ghost
								:loading="loadingImport"
							>
								<template #icon>
									<Icon :name="ImportIcon" />
								</template>
								<span v-if="!viewport.isLessThan('desktop')">Import</span>
							</n-button>
						</n-upload>

						<n-button
							:circle="viewport.isLessThan('desktop')"
							type="primary"
							ghost
							:loading="loadingExport"
							@click="handleExportData"
						>
							<template #icon>
								<Icon :name="ExportIcon" />
							</template>
							<span v-if="!viewport.isLessThan('desktop')">Export</span>
						</n-button>

						<n-button type="primary" :circle="viewport.isLessThan('desktop')" @click="showAddForm = true">
							<template #icon>
								<Icon :name="AddIcon" />
							</template>
							<span v-if="!viewport.isLessThan('desktop')">Tamu</span>
						</n-button>
					</n-space>
				</n-space>

				<n-space v-if="checkedRowKeys.length > 0" align="center">
					<n-text>{{ checkedRowKeys.length }} data telah dipilih</n-text>
					<n-button type="error" @click="handleDeleteItemsClicked(checkedRowKeys)">
						<template #icon>
							<Icon :name="DeleteIcon" />
						</template>
						Hapus
					</n-button>
				</n-space>

				<n-collapse-transition :show="showOptions">
					<GuestFilter v-model="selectedColumns" :agenda="agenda" @change="handleFilterChange" />
				</n-collapse-transition>

				<!-- :scroll-x="viewport.isLessThan('desktop') ? 900 : 1800" -->
				<n-data-table
					id="guests-table"
					size="small"
					:loading="loadingGuest"
					:columns="viewport.isLessThan('desktop') ? mobileColumns : visibleColumns"
					:data="guestsData"
					:row-key="rowKey"
					:checked-row-keys="checkedRowKeys"
					@update:checked-row-keys="handleCheck"
				/>

				<n-space align="center">
					<n-pagination
						v-model:page="fetchParam.page"
						:page-count="totalPages"
						:page-slot="7"
						@update:page="fetchGuests"
					/>
					<n-select
						v-model:value="fetchParam.limit"
						:options="optionsRowPerPage"
						@update:value="handleChangeRowPerPage"
					/>
				</n-space>
			</n-space>
		</template>
		<GuestAdd
			v-model:show="showAddForm"
			:agenda="agenda"
			@close="showAddForm = false"
			@after-submit="handleAfterSubmit"
		/>
		<GuestEdit
			v-model:show="showEditForm"
			:agenda="agenda"
			:selectedGuest="selectedGuest"
			@close="showEditForm = false"
			@after-submit="handleAfterSubmit"
		/>

		<!-- Floating Action Button -->
		<div id="fab-button" class="fab" @click="goToGuide">
			<Icon name="ion:book-outline" :size="24" color="white" />
		</div>

		<!-- Tour Modal -->
		<n-modal v-model:show="showTour" :mask-closable="false" preset="card" style="width: 90%; max-width: 500px">
			<template #header>
				<div class="flex items-center justify-between w-full">
					<span>{{ tourSteps[tourStep]?.title }}</span>
					<span class="text-sm opacity-60">{{ tourStep + 1 }}/{{ tourSteps.length }}</span>
				</div>
			</template>

			<div class="tour-content">
				<p>{{ tourSteps[tourStep]?.content }}</p>
			</div>

			<template #footer>
				<n-space justify="space-between">
					<n-button quaternary @click="skipTour">Lewati</n-button>
					<n-space>
						<n-button v-if="tourStep > 0" @click="tourStep--">Sebelumnya</n-button>
						<n-button type="primary" @click="nextTourStep">
							{{ tourStep === tourSteps.length - 1 ? "Selesai" : "Selanjutnya" }}
						</n-button>
					</n-space>
				</n-space>
			</template>
		</n-modal>
	</div>
</template>

<script setup>
import {
	NCollapseTransition,
	NUpload,
	NText,
	NButton,
	NInput,
	NSpace,
	NDataTable,
	NH1,
	NCard,
	useLoadingBar,
	useDialog,
	useMessage,
	NPagination,
	NSelect,
	NModal
} from "naive-ui"
import { read, utils, writeFileXLSX } from "xlsx"
import Icon from "~/components/common/Icon.vue"
import { useMainStore } from "@/stores/main"
import _debounce from "lodash/debounce"
import { useGuestsColumnDataTables } from "~/composables/useGuestsColumnDataTables"
import ShortUniqueId from "short-unique-id"
import { translateLevel } from "~/utils"

definePageMeta({
	name: "Guests",
	title: "Guests"
})

const AddIcon = "la:plus"
const DeleteIcon = "la:trash-alt"
const ReloadIcon = "la:redo-alt"
const ImportIcon = "la:cloud-upload-alt"
const ExportIcon = "la:cloud-download-alt"
const SettingIcon = "ion:filter"

const route = useRoute()
const router = useRouter()
const { createItems, getItemById, getItems, deleteItems } = useDirectusItems()
const loadingBar = useLoadingBar()
const message = useMessage()
const mainStore = useMainStore()
const user = useDirectusUser()
const dialog = useDialog()
const viewport = useViewport()

const listAccess = ref([])
const isAdmin = ref(user.value.role.name === "Administrator")
const agenda = ref(null)
const checkedRowKeys = ref([]) // Define checkedRowKeys
const selectedColumns = ref(["name", "status_relation", "level", "code_guest", "rsvp", "actions"]) // Default selected columns
const selectedGuest = ref(null)
const showEditForm = ref(false)
const showAddForm = ref(false)
const reloadLoading = ref(false)
const jsonRows = ref(null)
const loadingImport = ref(false)
const loadingExport = ref(false)
const showOptions = ref(false)

// Tour functionality
const showTour = ref(false)
const tourStep = ref(0)
const hasSeenTour = useLocalStorage(`guests-tour-${route.params.id}`, false)

const tourSteps = [
	{
		title: "Selamat Datang di Manajemen Tamu!",
		content: "Mari kami tunjukkan cara mengelola data tamu di halaman ini.",
		target: null
	},
	{
		title: "Statistik RSVP",
		content: "Di sini Anda dapat melihat statistik konfirmasi kehadiran tamu.",
		target: "stats-card"
	},
	{
		title: "Pencarian Tamu",
		content: "Gunakan kotak pencarian untuk mencari tamu berdasarkan nama, category, alamat, dan lainnya.",
		target: "search-input"
	},
	{
		title: "Tombol Aksi",
		content: "Gunakan tombol-tombol ini untuk filter, import, export, dan menambah tamu baru.",
		target: "action-buttons"
	},
	{
		title: "Tabel Data Tamu",
		content: "Tabel ini menampilkan semua data tamu. Klik nama tamu untuk mengedit.",
		target: "guests-table"
	},
	{
		title: "Tombol Panduan",
		content: "Klik tombol buku ini kapan saja untuk melihat panduan lengkap manajemen tamu.",
		target: "fab-button"
	}
]

const { load, guestAggregated } = useAggregateGuest()
const {
	load: loadRSVP,
	rsvpAggregated,
	going,
	notGoing,
	unconfirm,
	sumGoing,
	sumNotGoing,
	sumUnconfirm
} = useAggregateRSVP()

const goToGuide = () => {
	navigateTo(`https://bagimomen.my.id/tutorial_kelolatamu/`, {
		external: true
	})
}

const startTour = () => {
	showTour.value = true
	tourStep.value = 0
}

const nextTourStep = () => {
	if (tourStep.value < tourSteps.length - 1) {
		tourStep.value++
	} else {
		closeTour()
	}
}

const closeTour = () => {
	showTour.value = false
	hasSeenTour.value = true
}

const skipTour = () => {
	closeTour()
}

const convertFilterToGraphQL = filter => {
	const graphqlFilter = {}

	// Flatten _and or _or arrays into a single object
	if (filter._and) {
		filter._and.forEach(condition => {
			Object.entries(condition).forEach(([key, value]) => {
				// Special handling for `event`, converting its inner structure
				if (key === "event" && value._eq) {
					graphqlFilter[key] = { id: { _eq: value._eq } }
				} else {
					// Directly assign other conditions as they are
					graphqlFilter[key] = value
				}
			})
		})
	}

	return { filter: graphqlFilter }
}

const getAggregate = async () => {
	const aggregateParam = convertFilterToGraphQL(fetchParam.filter)
	try {
		await load(aggregateParam.filter, fetchParam.search)
		await loadRSVP(route.params.id)
		console.log(rsvpAggregated.value)
	} catch (error) {
		console.error(error)
		message.error(error)
	}
}

// data tables
const handleEditRowClicked = row => {
	selectedGuest.value = row
	showEditForm.value = true
}

const handleDeleteRowClicked = row => {
	const d = dialog.warning({
		title: "Konfirmasi Hapus Data",
		content: `Anda yakin hapus data ${row.name}?`,
		positiveText: "Hapus",
		negativeText: "Batalkan",
		onPositiveClick: async () => {
			d.loading = true
			await deleteGuests([row.id])
			d.loading = false
		}
	})
}
const {
	desktopColumns: columns,
	mobileColumns,
	rowKey
} = useGuestsColumnDataTables(handleEditRowClicked, handleDeleteRowClicked)

const visibleColumns = computed(() => {
	return columns.filter(col => selectedColumns.value.includes(col.key) || col.type === "selection")
})

const handleCheck = rowKeys => {
	checkedRowKeys.value = rowKeys
}

/* ------------------------------ Fetch Parameters ------------------------------ */
const fetchParam = reactive({
	filter: {
		_and: [
			{
				event: {
					_eq: route.params.id
				}
			}
		]
	},
	sort: ["-date_created", "-date_updated"],
	search: "",
	fields: ["*"],
	meta: "*",
	limit: 25,
	page: 1
})

const handleChangeRowPerPage = async () => {
	fetchParam.page = 1
	await fetchGuests()
}

const { data: guestsData, meta: guestsMeta, load: loadGuests, loading: loadingGuest } = useGetGuests()

const fetchGuests = async () => {
	loadingBar.start()
	try {
		await loadGuests(fetchParam)
		await getAggregate()
		loadingBar.finish()
	} catch (e) {
		loadingBar.error()
		console.error(e.message)
		message.error(e.message)
	} finally {
		loadingGuest.value = false
	}
}

const fetchAgenda = async () => {
	loadingBar.start()
	try {
		const item = await getItemById({
			collection: "event",
			id: route.params.id,
			params: {
				fields: ["*", "users.*"]
			}
		})
		item.users.forEach(user => {
			listAccess.value.push(user.directus_users_id)
		})
		agenda.value = item
		loadingBar.finish()
	} catch (e) {
		console.error(e.message)
		message.error(e.message)
		loadingBar.error()
	}
}

const handleClear = async () => {
	fetchParam.search = ""
	fetchParam.page = 1
	await fetchGuests()
}

const delayedSearch = _debounce(async () => {
	fetchParam.page = 1
	await fetchGuests()
}, 500)

const handleDeleteItemsClicked = () => {
	const d = dialog.warning({
		title: "Konfirmasi Hapus Data",
		content: `Anda yakin hapus data sebanyak ${checkedRowKeys.value.length}?`,
		positiveText: "Hapus",
		negativeText: "Batalkan",
		onPositiveClick: async () => {
			d.loading = true
			await deleteGuests([...checkedRowKeys.value])
			d.loading = false
			checkedRowKeys.value = []
		}
	})
}

const deleteGuests = async items => {
	try {
		await deleteItems({
			collection: "guest",
			items: [...items]
		})
		message.success("Data dihapus")
		await fetchGuests()
	} catch (e) {
		console.error(e)
		message.error(e.message)
	}
}

const refresh = e => {
	reloadLoading.value = true
	mainStore.softReload()
	reloadLoading.value = false
	return e
}

const { randomUUID } = new ShortUniqueId({ length: 4 })

const handleFileChange = async ({ file }) => {
	loadingImport.value = true
	try {
		loadingBar.start()
		// Check if file object has a raw or file property and use it accordingly
		const targetFile = file.raw || file.file || file

		// Read the file as an array buffer
		const arrayBuffer = await targetFile.arrayBuffer()
		const workbook = read(arrayBuffer, { type: "array" })

		// Assuming the first sheet
		const firstSheetName = workbook.SheetNames[0]
		const worksheet = workbook.Sheets[firstSheetName]

		// Convert sheet data to JSON
		const jsonData = utils.sheet_to_json(worksheet)

		jsonRows.value = jsonData

		const prefix = `${agenda.value.code_event}-`

		jsonRows.value.forEach((row, index) => {
			row["event"] = agenda.value.id

			// penngkodean otomatis
			if (!row["code_guest"]) {
				row["code_guest"] = prefix + randomUUID()
			}
		})
		// Optionally send the data to Directus
		// await uploadToDirectus(jsonData);
		await createItems({ collection: "guest", items: jsonRows.value })
		await fetchGuests()
		message.success("Data tersimpan")
		loadingBar.finish()
		jsonRows.value = null
	} catch (error) {
		console.error("Error processing the file:", error)
		message.error(error.message)
		loadingBar.error()
	} finally {
		loadingImport.value = false
	}
}

const handleExportData = async () => {
	loadingExport.value = true
	try {
		loadingBar.start()

		// Create a copy of the current fetch parameters but set limit to a high number to get all guests
		const exportParams = { ...fetchParam }
		exportParams.limit = 10000 // Set a high limit to get all guests

		// Add fields to include regist_by relation
		exportParams.fields = ["*", "regist_by.first_name", "regist_by.last_name"]

		// Fetch all guests for export
		const { data: allGuests } = await getItems({
			collection: "guest",
			params: exportParams
		})

		if (!allGuests || allGuests.length === 0) {
			message.warning("Tidak ada data tamu untuk diekspor")
			return
		}

		// Format the data for export
		const exportData = allGuests.map(guest => {
			// Format usher name from regist_by relation
			let usherName = ""
			if (guest.regist_by) {
				const firstName = guest.regist_by.first_name || ""
				const lastName = guest.regist_by.last_name || ""
				usherName = `${firstName} ${lastName}`.trim()
			}

			// Format gift data
			let giftText = ""
			if (guest.gift && Array.isArray(guest.gift) && guest.gift.length > 0) {
				const giftTranslations = {
					1: "Amplop",
					2: "kado",
					3: "transfer"
				}

				const translatedGifts = guest.gift.map(giftValue => {
					return giftTranslations[giftValue] || giftValue
				})

				giftText = translatedGifts.join(", ")
			}

			return {
				Nama: guest.name || "",
				"Status/Relasi": guest.status_relation || "",
				"Kode Tamu": guest.code_guest || "",
				Level: translateLevel(guest.level) || "Reguler",
				Sesi: guest.shift || "",
				Meja: guest.table || "",
				Kategori: guest.category || "",
				Label: guest.label || "",
				Alamat: guest.address || "",
				"No HP": guest.phone || "",
				"Jumlah Tamu": guest.amount_guest || 0,
				"Jumlah Souvenir": guest.amount_souvenir || 0,
				Catatan: guest.note || "",
				Hadiah: giftText,
				Titipan: guest.entrust ? "Ya" : "Tidak",
				"Dititipkan Pada": guest.entrust_by || "",
				RSVP: guest.rsvp ? (guest.rsvp === "0" ? "Tidak Hadir" : "Hadir") : "Belum Konfirmasi",
				Kehadiran: guest.presence ? "Hadir" : "Belum Hadir",
				"Waktu Kehadiran": guest.attendance_time || "",
				Usher: usherName
			}
		})

		// Create a worksheet
		const worksheet = utils.json_to_sheet(exportData)

		// Create a workbook
		const workbook = utils.book_new()
		utils.book_append_sheet(workbook, worksheet, "Guests")

		// Generate filename with event code and date
		const date = new Date()
		const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`

		// Ask user for format preference
		dialog.create({
			title: "Format Ekspor",
			content: "Pilih format ekspor data tamu",
			positiveText: "XLSX",
			negativeText: "CSV",
			onPositiveClick: () => {
				// Export as XLSX
				const filename = `${agenda.value.title || "event"}--${agenda.value.code_event || "event"}_guests_${dateStr}.xlsx`
				writeFileXLSX(workbook, filename)
				message.success("Data berhasil diekspor dalam format XLSX")
			},
			onNegativeClick: () => {
				// Export as CSV
				const csv = utils.sheet_to_csv(worksheet)
				const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
				const filename = `${agenda.value.title || "event"}--${agenda.value.code_event || "event"}_guests_${dateStr}.csv`

				// Create a download link and trigger it
				const link = document.createElement("a")
				const url = URL.createObjectURL(blob)
				link.setAttribute("href", url)
				link.setAttribute("download", filename)
				link.style.visibility = "hidden"
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)

				message.success("Data berhasil diekspor dalam format CSV")
			}
		})

		loadingBar.finish()
	} catch (error) {
		console.error("Error exporting data:", error)
		message.error(error.message || "Gagal mengekspor data")
		loadingBar.error()
	} finally {
		loadingExport.value = false
	}
}

const handleAfterSubmit = async () => {
	await fetchGuests()
	showEditForm.value = false
	showAddForm.value = false
}

const totalPages = computed(() => {
	return Math.ceil(guestsMeta.value.filter_count / fetchParam.limit)
})

const handleFilterChange = async filterParam => {
	fetchParam.filter = filterParam
	await delayedSearch()
}

onMounted(async () => {
	await fetchAgenda()
	await fetchGuests()

	// Show tour if user hasn't seen it before
	if (!hasSeenTour.value) {
		setTimeout(() => {
			startTour()
		}, 1000) // Delay to ensure page is fully loaded
	}
})
</script>

<style scoped>
@keyframes pulse-glow {
	0% {
		transform: scale(1);
		box-shadow:
			0 4px 12px rgba(0, 0, 0, 0.15),
			0 0 20px var(--primary-030-color, rgba(255, 97, 201, 0.3));
	}
	50% {
		transform: scale(1.05);
		box-shadow:
			0 6px 16px rgba(0, 0, 0, 0.2),
			0 0 40px var(--primary-050-color, rgba(255, 97, 201, 0.5)),
			0 0 60px var(--primary-030-color, rgba(255, 97, 201, 0.3));
	}
	100% {
		transform: scale(1);
		box-shadow:
			0 4px 12px rgba(0, 0, 0, 0.15),
			0 0 20px var(--primary-030-color, rgba(255, 97, 201, 0.3));
	}
}

.fab {
	position: fixed;
	bottom: 24px;
	right: 24px;
	width: 56px;
	height: 56px;
	background-color: var(--primary-color);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 1000;
	transition: transform 0.3s ease;
	animation: pulse-glow 1.5s ease-in-out infinite;
}

.fab:hover {
	transform: scale(1.1);
	animation-play-state: paused;
	box-shadow:
		0 6px 16px rgba(0, 0, 0, 0.2),
		0 0 40px var(--primary-060-color, rgba(255, 97, 201, 0.6)),
		0 0 60px var(--primary-040-color, rgba(255, 97, 201, 0.4));
}

.fab:active {
	transform: scale(0.95);
}

@media (max-width: 768px) {
	.fab {
		bottom: 20px;
		right: 20px;
		width: 48px;
		height: 48px;
	}
}

@media (prefers-reduced-motion: reduce) {
	.fab {
		animation: none;
	}
}

.tour-content {
	padding: 16px 0;
	line-height: 1.6;
}

.tour-content p {
	margin: 0;
	color: var(--text-color);
}
</style>
