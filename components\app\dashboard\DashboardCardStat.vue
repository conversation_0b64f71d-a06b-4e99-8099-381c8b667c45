<template>
	<n-card class="card">
		<template #header>
			<n-flex align="stretch" size="small">
				<Icon :name="icon" />
				<span>{{ title }}</span>
			</n-flex>
		</template>
		<template #header-extra>
			<n-button strong secondary circle @click="() => (show = !show)">
				<template #icon>
					<Icon :size="20" :name="AddIcon" :style="{ transform: show ? 'rotate(45deg)' : 'rotate(0deg)' }" />
				</template>
			</n-button>
		</template>
		<n-row>
			<n-col :span="14">
				<n-statistic :label="valueText" tabular-nums>
					<!-- <n-number-animation show-separator :from="0" :to="countPositive" /> -->
					{{ countPositive }}
					<template #suffix>
						/
						<!-- <n-number-animation show-separator :from="0" :to="countNegative" /> -->
						{{ countNegative }}
					</template>
				</n-statistic>
			</n-col>
			<n-col class="text-right" :span="10">
				<n-statistic :label="totalText" tabular-nums>
					<!-- <n-number-animation show-separator :from="0" :to="total" /> -->
					{{ total }}
				</n-statistic>
			</n-col>
		</n-row>
		<n-collapse-transition class="my-2" :show="show">
			<n-flex vertical size="medium">
				<n-row v-for="item in detail" :key="item.title">
					<!-- {{ item.title }} {{ item.positive }} / {{ item.negative }} -->
					<n-col :span="12">{{ item.title }} : {{ item.positive }} / {{ item.negative }}</n-col>
					<n-col class="text-right" :span="12">{{ item.total }}</n-col>
				</n-row>
			</n-flex>
		</n-collapse-transition>
	</n-card>
</template>

<script setup>
import { NButton, NCard, NFlex, NRow, NCol, NStatistic, NNumberAnimation, NCollapseTransition } from "naive-ui"
const AddIcon = "carbon:add"
const show = ref(false)
defineProps({
	title: {
		type: String,
		required: true
	},
	valueText: {
		type: String,
		required: true
	},
	totalText: {
		type: String,
		required: true
	},
	icon: {
		type: String,
		required: true
	},
	countPositive: {
		type: Number,
		required: true
	},
	countNegative: {
		type: Number,
		required: true
	},
	total: {
		type: Number,
		required: true
	},
	detail: {
		type: Array,
		required: true
	}
})
</script>

<style scoped lang="scss"></style>
