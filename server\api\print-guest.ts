export default defineEventHandler(async event => {
	// Parse query parameters
	const query = getQuery(event)

	const {
		title = "<PERSON><PERSON> dan <PERSON>", // Default name
		name = "KEL Wari", // Default guest name
		qrvalue = "qrvalue" // Default QR value
	} = query

	// Construct the response object
	const response = {
		1: {
			type: 0,
			content: "********************************",
			bold: 0,
			align: 1,
			format: 0
		},
		2: {
			type: 0,
			content: title,
			bold: 1,
			align: 1,
			format: 3
		},
		3: {
			type: 0,
			content: "********************************",
			bold: 0,
			align: 1,
			format: 0
		},
		4: {
			type: 0,
			content: " ",
			bold: 0,
			align: 1,
			format: 0
		},
		5: {
			type: 0,
			content: "Tamu Undangan :",
			bold: 0,
			align: 1,
			format: 0
		},
		6: {
			type: 0,
			content: name,
			bold: 0,
			align: 1,
			format: 0
		},
		7: {
			type: 3,
			value: qrvalue,
			size: 30,
			align: 1
		},
		8: {
			type: 0,
			content: " ",
			bold: 0,
			align: 1,
			format: 0
		},
		9: {
			type: 0,
			content: "*Harap disimpan, struk ini digunakan untuk mengambil souvenir",
			bold: 0,
			align: 1,
			format: 0
		},
		10: {
			type: 0,
			content: "********************************",
			bold: 0,
			align: 1,
			format: 0
		},
		11: {
			type: 0,
			content: " ",
			bold: 0,
			align: 1,
			format: 0
		}
	}

	return response
})
