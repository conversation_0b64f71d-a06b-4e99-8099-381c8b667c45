export const useAggregateGuest = () => {
	const { $directus } = useNuxtApp()
	const guestAggregated = ref(null)
	const loading = ref(false)

	const load = async (filter: any, search: string = "") => {
		loading.value = true
		const { token } = useDirectusToken()
		await $directus.setToken(token.value)
		const result = await $directus.query(
			`
      query AggregateGuest($filter: guest_filter, $search: String) {
        guest_aggregated(
            search: $search
            filter: $filter,            
        ) {
            count {
                id
            }
            sum {
                amount_guest
            }
        }    
    }`,
			{ filter: filter, search: search }
		)

		guestAggregated.value = result
		loading.value = false
	}

	return { load, loading, guestAggregated }
}
