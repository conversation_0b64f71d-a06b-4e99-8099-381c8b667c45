<template>
	<div
		class="qr-card border-2 p-0 justify-center text-center flex flex-col"
		:style="{
			backgroundColor: cardInputModel.backgroundColor,
			fontFamily: cardInputModel.textFont
		}"
	>
		<div class="text-center" style="margin-top: 4%; padding: 2%">
			<h4
				v-resize-text="{
					ratio: 1.4,
					minFontSize: 10,
					maxFontSize: 500,
					delay: 200
				}"
				:style="{
					color: cardInputModel.titleColor,
					fontFamily: cardInputModel.textFont
				}"
			>
				{{ cardInputModel.title }}
			</h4>
			<h2
				v-resize-text="{
					ratio: 1.1,
					minFontSize: 10,
					maxFontSize: 500,
					delay: 200
				}"
				:style="{
					fontFamily: cardInputModel.nameFont,
					color: cardInputModel.nameColor
				}"
			>
				{{ cardInputModel.name }}
			</h2>
		</div>
		<div ref="qrsize" class="w-[40%] text-center justify-center mx-auto">
			<n-qr-code class="p-0 my-2 border-1 mx-auto" :value="qr" type="svg" :size="qrWidth" />
		</div>
		<n-p
			v-resize-text="{
				ratio: 2.8,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			:style="{
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			<template v-if="cardInputModel.language == 0">
				Kepada Yth.
				<br />
				Bapak/Ibu/Saudara/i
			</template>
			<template v-if="cardInputModel.language == 1">
				Dear.
				<br />
				Mr/Mrs/Ms
			</template>
		</n-p>
		<div
			v-resize-text="{
				ratio: 1.5,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			class="font-bold"
			:style="{
				marginBottom: '5%',
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ name }}
		</div>
		<div
			v-resize-text="{
				ratio: 1.7,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			class="font-semibold"
			:style="{
				marginBottom: '1.5%',
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ cardInputModel.location }}
		</div>
		<div
			v-resize-text="{
				ratio: 2.5,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			:style="{
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ cardInputModel.date }}
		</div>
		<div
			v-resize-text="{
				ratio: 2.5,
				minFontSize: 10,
				maxFontSize: 500,
				delay: 200
			}"
			:style="{
				color: cardInputModel.textColor,
				fontFamily: cardInputModel.textFont
			}"
		>
			{{ cardInputModel.time }}
		</div>
		<div class="mt-auto" style="margin-bottom: 7%; padding-left: 5%; padding-right: 5%">
			<n-p
				v-resize-text="{
					ratio: 2.5,
					minFontSize: 10,
					maxFontSize: 500,
					delay: 200
				}"
				class="text-center font-light"
				:style="{
					color: cardInputModel.textColor,
					fontFamily: cardInputModel.textFont
				}"
			>
				{{ cardInputModel.info }}
			</n-p>
		</div>
	</div>
</template>

<script setup>
import { NImage, NGrid, NGridItem, NRow, NCol, NQrCode, NFlex, NP } from "naive-ui"
import VueResizeText from "vue3-resize-text"
import { Icon } from "@iconify/vue"

defineProps({
	cardInputModel: {
		type: Object,
		required: true
	},
	name: {
		type: String,
		required: true
	},
	qr: {
		type: String,
		required: true
	}
})

const vResizeText = VueResizeText.ResizeText

const qrsize = ref(null)
const { width: qrWidth } = useElementSize(qrsize)
</script>

<style></style>
