<template>
	<div class="page">
		<n-h1 prefix="bar" class="mb-4">
			<h1>Camera Input Example</h1>
		</n-h1>

		<n-space vertical size="large">
			<n-card title="Image Input Form">
				<n-form :model="formModel" label-placement="left" label-width="120">
					<n-form-item label="Name">
						<n-input v-model:value="formModel.name" placeholder="Enter your name" />
					</n-form-item>

					<n-form-item label="Profile Photo">
						<ImageInput
							v-model="formModel.photo"
							camera-title="Take Profile Photo"
							camera-button-text="Take Photo"
							upload-button-text="Upload Photo"
							aspect-ratio="1/1"
						/>
					</n-form-item>

					<n-form-item>
						<n-button type="primary" @click="submitForm" :disabled="!isFormValid">Submit</n-button>
					</n-form-item>
				</n-form>
			</n-card>

			<n-card v-if="submitted" title="Submitted Data">
				<n-descriptions bordered>
					<n-descriptions-item label="Name">
						{{ formModel.name }}
					</n-descriptions-item>
				</n-descriptions>

				<n-space vertical class="mt-4">
					<div v-if="formModel.photo">
						<h3>Profile Photo</h3>
						<img :src="formModel.photo" style="max-width: 300px; max-height: 300px" />
					</div>

					<div v-if="formModel.idCard">
						<h3>ID Card</h3>
						<img :src="formModel.idCard" style="max-width: 300px; max-height: 200px" />
					</div>
				</n-space>
			</n-card>
		</n-space>
	</div>
</template>

<script setup>
import { ref, computed } from "vue"
import {
	NH1,
	NSpace,
	NCard,
	NForm,
	NFormItem,
	NInput,
	NButton,
	NDescriptions,
	NDescriptionsItem,
	useMessage
} from "naive-ui"
import ImageInput from "~/components/common/ImageInput.vue"

definePageMeta({
	name: "CameraExample",
	title: "Camera Example"
})

const message = useMessage()

// Form model
const formModel = ref({
	name: "",
	photo: "",
	idCard: ""
})

const submitted = ref(false)

// Computed property to check if form is valid
const isFormValid = computed(() => {
	return formModel.value.name && (formModel.value.photo || formModel.value.idCard)
})

// Submit form
const submitForm = () => {
	if (!isFormValid.value) {
		message.error("Please fill in the required fields")
		return
	}

	// In a real application, you would process the form data here
	// For example, convert the data URLs to files and upload them to a server

	submitted.value = true
	message.success("Form submitted successfully")

	// Log the form data (truncated for brevity)
	console.log("Form submitted:", {
		name: formModel.value.name,
		photo: formModel.value.photo ? `${formModel.value.photo.substring(0, 30)}...` : null,
		idCard: formModel.value.idCard ? `${formModel.value.idCard.substring(0, 30)}...` : null
	})
}
</script>

<style scoped>
.page {
	padding: 20px;
	max-width: 800px;
	margin: 0 auto;
}
</style>
