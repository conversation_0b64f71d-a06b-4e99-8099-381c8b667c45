<template>
	<div class="page">
		<n-h1 prefix="bar" class="mb-4">
			<h1>PANDUAN PENGGUNAAN</h1>
		</n-h1>
		
		<n-card>
			<n-space vertical size="large">
				<div>
					<n-h3>Cara Mengirim Pesan</n-h3>
					<n-ol>
						<n-li>Pilih tab "Tamu" untuk melihat daftar tamu</n-li>
						<n-li>Klik pada nama tamu yang ingin dikirim pesan</n-li>
						<n-li>Pilih template pesan yang sesuai</n-li>
						<n-li>Klik tombol kirim pesan (WhatsApp)</n-li>
						<n-li>Tandai sebagai terkirim dengan centang checkbox</n-li>
					</n-ol>
				</div>

				<div>
					<n-h3>Fitur Template</n-h3>
					<n-ul>
						<n-li>Tab "Template" berisi template pesan yang dapat disesuaikan</n-li>
						<n-li>Anda dapat menyalin template dan mengeditnya sesuai kebutuhan</n-li>
						<n-li>Template akan otomatis mengisi nama tamu dan detail acara</n-li>
					</n-ul>
				</div>

				<div>
					<n-h3>Tips Penggunaan</n-h3>
					<n-alert type="info">
						<n-ul>
							<n-li>Pastikan nomor WhatsApp tamu sudah benar sebelum mengirim</n-li>
							<n-li>Gunakan template yang sesuai dengan jenis acara</n-li>
							<n-li>Tandai pesan yang sudah terkirim untuk tracking yang lebih baik</n-li>
						</n-ul>
					</n-alert>
				</div>

				<div>
					<n-h3>Ikon dan Fungsinya</n-h3>
					<n-space vertical>
						<n-flex align="center">
							<Icon :size="16" name="ion:checkmark-circle-outline" color="#00B27B" />
							<span>Tandai sebagai terkirim</span>
						</n-flex>
						<n-flex align="center">
							<Icon :size="16" name="ion:copy-outline" />
							<span>Salin pesan ke clipboard</span>
						</n-flex>
						<n-flex align="center">
							<Icon :size="16" name="logos:whatsapp-icon" />
							<span>Kirim pesan melalui WhatsApp</span>
						</n-flex>
					</n-space>
				</div>
			</n-space>
		</n-card>
	</div>
</template>

<script setup>
import { NH1, NH3, NCard, NSpace, NOl, NLi, NUl, NAlert, NFlex } from "naive-ui"

definePageMeta({
	name: "Guide",
	title: "Panduan"
})
</script>

<style scoped></style>
