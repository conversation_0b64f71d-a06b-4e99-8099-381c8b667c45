<template>
	<div class="logo" v-if="isDark && !mini">
		<n-button v-if="isMenu" style="margin-right: 4px" circle>
			<template #icon>
				<Icon name="carbon:menu" />
			</template>
		</n-button>
		<n-gradient-text v-if="isMenu" class="logo-font" :size="24" type="primary">BAGIMOMEN</n-gradient-text>
		<img v-else src="@/assets/images/brand-logo_dark.svg?url" />
	</div>
	<div class="logo" v-else-if="isLight && !mini">
		<n-button v-if="isMenu" style="margin-right: 4px" circle>
			<template #icon>
				<Icon name="carbon:menu" />
			</template>
		</n-button>
		<n-gradient-text v-if="isMenu" class="logo-font" :size="24" type="primary">BAGIMOMEN</n-gradient-text>
		<img v-else src="@/assets/images/brand-logo_light.svg?url" />
	</div>
	<div class="logo" v-else-if="isDark && mini">
		<n-button v-if="isMenu" style="margin-right: 4px" circle>
			<template #icon>
				<Icon name="carbon:menu" />
			</template>
		</n-button>
		<n-gradient-text v-if="isMenu" class="logo-font" :size="24" type="primary">BAGIMOMEN</n-gradient-text>
		<img v-else src="@/assets/images/brand-icon_dark.svg?url" />
	</div>
	<div class="logo" v-else-if="isLight && mini">
		<n-button v-if="isMenu" style="margin-right: 4px" circle>
			<template #icon>
				<Icon name="carbon:menu" />
			</template>
		</n-button>
		<n-gradient-text v-if="isMenu" class="logo-font" :size="24" type="primary">BAGIMOMEN</n-gradient-text>
		<img v-else src="@/assets/images/brand-icon_light.svg?url" />
	</div>
</template>

<script lang="ts" setup>
import { NButton, NGradientText } from "naive-ui"
import { useThemeStore } from "@/stores/theme"
import { computed, toRefs } from "vue"

defineOptions({
	name: "Logo"
})

const props = withDefaults(
	defineProps<{
		mini: boolean
		dark?: boolean
		menu?: boolean
	}>(),
	{ dark: undefined }
)
const { mini, dark, menu } = toRefs(props)

const themeStore = useThemeStore()

const isDark = computed<boolean>(() => dark.value ?? themeStore.isThemeDark)
const isLight = computed<boolean>(() => !dark.value || themeStore.isThemeLight)
const isMenu = computed<boolean>(() => menu.value ?? false)
</script>

<style lang="scss" scoped>
.logo {
	height: 100%;
	display: flex;
	align-items: center;

	img {
		max-height: 32px;
		display: block;
		height: 100%;
	}

	&.fade-enter-active,
	&.fade-leave-active {
		transition: opacity var(--sidebar-anim-ease) var(--sidebar-anim-duration);
	}

	&.fade-enter-from,
	&.fade-leave-to {
		opacity: 0;
	}
}
</style>
