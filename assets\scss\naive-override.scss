.n-modal-mask {
	backdrop-filter: blur(3px);
}
.n-modal,
.n-card.n-modal[role] {
	background-color: rgba(var(--modal-color-rgb), 0.7);
	backdrop-filter: blur(20px);
	max-width: 90%;
	margin: 10vh auto;
}

.n-image-preview-overlay {
	backdrop-filter: blur(3px);
}
.n-image-preview-toolbar {
	box-sizing: content-box;

	* {
		box-sizing: content-box;
	}
}
.n-slider {
	box-sizing: content-box;
}
.n-calendar * {
	box-sizing: content-box;
}
.n-badge {
	.n-badge-sup {
		top: inherit;
	}
}
.n-dropdown-menu {
	&.n-dropdown-menu--scrollable {
		max-height: 60vh;
	}
}
.n-card {
	& > .n-card-header {
		div.n-card-header__main {
			//font-family: var(--font-family-display);
			font-weight: 700;
		}
	}
	& > .n-card__content {
		max-height: 100%;
	}
}
.n-avatar .n-avatar__text {
	transform: translateX(-50%) translateY(-50%) scale(1);
}

// popover max-width
.v-binder-follower-content {
	max-width: calc(100vw - (var(--view-padding) * 2));
}
