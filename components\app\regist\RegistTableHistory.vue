<template>
	<n-table>
		<thead>
			<tr>
				<th><PERSON>a</th>
				<th>Level</th>
				<th>Pax</th>
				<th>Kedatangan</th>
				<th>usher</th>
			</tr>
		</thead>
		<tbody>
			<tr v-for="guest of guests" v-wave :key="guest.id" @click="emitSelect(guest)">
				<td>
					<div class="product flex items-center">
						<div class="product-info">
							<div class="product-name">
								{{ guest.name }}
							</div>
							<div class="product-category">
								{{ guest.status_relation || "-" }}
							</div>
						</div>
					</div>
				</td>
				<td>
					<div class="stock">
						<n-tag v-if="guest.level === 3" round type="warning">VVIP</n-tag>
						<n-tag v-if="guest.level === 2" round type="warning">VIP</n-tag>
						<n-tag v-if="guest.level === 1" round>Reguler</n-tag>
					</div>
				</td>
				<td>
					<div class="price">{{ guest.amount_guest }} Pax</div>
				</td>
				<td>
					<div class="stock">
						{{ dayjs(guest.attendance_time).format("HH:mm DD-MM-YYYY") }}
					</div>
					<n-tag v-if="guest.presence && guest.on_Site" round :bordered="false" size="small" type="success">
						On Site
						<template #icon>
							<Icon name="icon-park-solid:check-one" />
						</template>
					</n-tag>
					<n-tag v-if="guest.presence && !guest.on_Site" round :bordered="false" size="small" type="success">
						Hadir
						<template #icon>
							<Icon name="icon-park-solid:check-one" />
						</template>
					</n-tag>
				</td>
				<td>
					<div class="stock">
						{{ usher(guest) }}
					</div>
				</td>
			</tr>
		</tbody>
	</n-table>
</template>

<script setup>
import { NTable, NTag } from "naive-ui"
import dayjs from "dayjs"

defineProps({
	guests: Array
})
const emits = defineEmits(["select"])
const emitSelect = guest => {
	emits("select", guest)
}

const usher = guest => {
	let usher = "-"
	if (guest.regist_by) {
		if (guest.regist_by.first_name) {
			usher = `${guest.regist_by.first_name}`
		}
		if (guest.regist_by.last_name) {
			usher += ` ${guest.regist_by.last_name}`
		}
	}
	return usher
}
</script>

<style scoped lang="scss">
.product {
	.product-image {
		.n-image {
			:deep(img) {
				border-radius: var(--border-radius-small);
			}
		}
	}

	.product-info {
		.product-name {
			font-weight: 500;
			font-size: 16px;
			line-height: 1.2;
		}
		.product-category {
			opacity: 0.6;
		}
	}
}

.price {
	white-space: nowrap;
}

.orders {
	text-align: right;
	.orders-value {
		white-space: nowrap;
	}
}
</style>
