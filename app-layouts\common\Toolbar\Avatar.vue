<template>
	<n-dropdown :options="options" placement="bottom-end" @select="handleSelect">
		<Avatar :size="32" variant="beam" :name="user.id" />
	</n-dropdown>
</template>

<script setup>
import { NDropdown } from "naive-ui"
import { renderIcon } from "@/utils"
import Avatar from "vue-boring-avatars"
import { useRouter } from "vue-router"
import { ref } from "vue"

const UserIcon = "ion:person-outline"
const LogoutIcon = "ion:log-out-outline"
// const DocsIcon = "ion:book-outline"

const { logout } = useDirectusAuth()
const user = useDirectusUser()
const username = `${user.value?.first_name} ${user.value?.last_name}` || "unknown"
console.log(user.value)

defineOptions({
	name: "Avatar"
})

const router = useRouter()

const options = ref([
	{
		label: "Profile",
		key: "route-Profile",
		icon: renderIcon(UserIcon),
		url: "/profile"
	},
	// {
	// 	label: () =>
	// 		h(
	// 			"a",
	// 			{
	// 				href: "https://pinx-docs.vercel.app/",
	// 				target: "_blank",
	// 				rel: "noopenner noreferrer"
	// 			},
	// 			"Documentation"
	// 		),
	// 	key: "documentation",
	// 	icon: renderIcon(DocsIcon)
	// },
	{
		label: "Logout",
		key: "route-Logout",
		icon: renderIcon(LogoutIcon),
		url: "/auth/logout"
	}
])

const handleSelect = async key => {
	if (key === "route-Profile") {
		navigateTo(options.value[0].url)
	} else if (key === "route-Logout") {
		await logout()
		navigateTo("/auth/signin")
	}
}
</script>
