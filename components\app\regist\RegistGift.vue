<template>
	<n-flex vertical>
		<ResultError v-if="fetchError" @refresh="refresh" />
		<template v-else>
			<n-grid x-gap="8" y-gap="8" :cols="viewport.isLessThan('desktop') ? 2 : 4">
				<n-gi>
					<RegistCardIcon :value="aggregatedGuestEvent.gift.total" title="TOTAL" icon="heroicons:gift-top" />
				</n-gi>
				<n-gi>
					<RegistCardIcon :value="aggregatedGuestEvent.gift.cash" title="AMPLOP" icon="heroicons:envelope" />
				</n-gi>
				<n-gi>
					<RegistCardIcon :value="aggregatedGuestEvent.gift.present" title="KADO" icon="heroicons:gift" />
				</n-gi>
				<n-gi>
					<RegistCardIcon
						:value="aggregatedGuestEvent.gift.transfer"
						title="TRANSFER"
						icon="heroicons:credit-card"
					/>
				</n-gi>
			</n-grid>
			<!-- search -->
			<n-space align="center" :size="viewport.isLessThan('tablet') ? 'small' : 'medium'">
				<n-input
					v-model:value="fetchParam.search"
					placeholder="Cari Tamu"
					clearable
					@keyup.enter="fetchGuests"
					@input="delayedSearch"
					@clear="handleClear"
				/>
				<n-button circle type="primary" ghost @click="showOnSiteForm = true">
					<template #icon>
						<Icon :name="OnsiteIcon" />
					</template>
				</n-button>
				<n-button circle type="primary" ghost @click="showGiftForm = true">
					<template #icon>
						<Icon :name="GiftIcon" />
					</template>
				</n-button>
				<n-button circle type="primary" :loading="reloadLoading" ghost @click="handleReload">
					<template #icon>
						<Icon :name="ReloadIcon" />
					</template>
				</n-button>
			</n-space>

			<!-- skeleton -->
			<SkeletonList v-if="fetchStatus === 'idle'" />

			<template v-if="guestsData">
				<!-- empty -->
				<EmptyData v-if="guestsData.length == 0" @refresh="fetchGuests" />

				<n-flex>
					<!-- @select="handleSelect" -->
					<!-- guests mobile -->
					<RegistGuestsGiftMobile v-if="viewport.isLessThan('desktop')" :guests="guestsData" />
					<!-- guest desktop -->
					<RegistTableGift v-else :guests="guestsData" />
				</n-flex>

				<!-- pagination -->
				<n-pagination
					v-model:page="fetchParam.page"
					:page-count="totalPages"
					:page-slot="7"
					@update:page="fetchGuests"
				/>
			</template>

			<!-- on site form -->
			<RegistDrawerOnSite
				v-model:show="showOnSiteForm"
				:agenda="agenda"
				@close="showOnSiteForm = !showOnSiteForm"
				@after-submit="handleAfterSubmit"
				@print-content="printContent"
				@print-entrust="printEntrust"
			/>

			<!-- gift form -->
			<RegistDrawerGift
				v-model:show="showGiftForm"
				:agenda="agenda"
				@close="showGiftForm = !showGiftForm"
				@after-submit="handleAfterSubmit"
				@print-entrust="printEntrust"
			/>
		</template>
	</n-flex>
</template>

<script setup>
import { NFlex, NSpace, NInput, NButton, NPagination, NGrid, NGi, useMessage, useLoadingBar } from "naive-ui"
import { useMainStore } from "@/stores/main"
import _debounce from "lodash/debounce"

const props = defineProps({
	agenda: Object
})

const emit = defineEmits(["print-content", "print-entrust"])

const printContent = guest => {
	emit("print-content", guest)
}

const printEntrust = guest => {
	emit("print-entrust", guest)
}

const OnsiteIcon = "mdi:account-plus-outline"
const GiftIcon = "carbon:gift"
const ReloadIcon = "mdi:reload"

const viewport = useViewport()
const mainStore = useMainStore()
const message = useMessage()
const loadingBar = useLoadingBar()
const { getItems } = useDirectusItems()

const guestsData = ref(null)
const guestsMeta = ref(null)
const fetchError = ref(null)
const fetchStatus = ref("idle")
const selectedGuest = ref(null)
const showForm = ref(false)
const showOnSiteForm = ref(false)
const showGiftForm = ref(false)

const reloadLoading = ref(false)

const fetchParam = ref({
	filter: {
		event: {
			_eq: props.agenda.id
		},
		"count(gift)": { _neq: 0 }
	},
	sort: ["-date_created"],
	search: "",
	fields: ["*", "regist_by.first_name", "regist_by.last_name"],
	meta: "*",
	limit: 5,
	page: 1
})

const { load: loadAggregate, aggregatedGuestEvent } = useFetchAggregateGuestEvent(props.agenda.id)

const fetchAggregate = async () => {
	fetchStatus.value = "pending"
	try {
		await loadAggregate()
	} catch (e) {
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
		} else {
			console.error(e)
			message.error(e)
		}
		fetchStatus.value = "error"
	}
}

const fetchGuests = async () => {
	fetchStatus.value = "pending"
	loadingBar.start()
	try {
		const items = await getItems({
			collection: "guest",
			params: {
				...fetchParam.value
			}
		})
		guestsData.value = items.data
		guestsMeta.value = items.meta
		fetchStatus.value = "success"
		loadingBar.finish()
	} catch (e) {
		loadingBar.error()
		fetchStatus.value = "error"
		if (e.errors) {
			const errorMessage = e.errors[0].message
			console.error(errorMessage)
			message.error(errorMessage)
			fetchError.value = errorMessage
		} else {
			console.error(e)
			message.error(e)
			fetchError.value = e
		}
	}
}

const handleSelect = guest => {
	showForm.value = true
	selectedGuest.value = guest
}

const totalPages = computed(() => {
	return Math.ceil(guestsMeta.value.filter_count / fetchParam.value.limit)
})

const handleFilter = async () => {
	fetchParam.value.page = 1
	await fetchGuests()
}

const handleClear = async () => {
	fetchParam.value.search = ""
	fetchParam.value.page = 1
	await fetchGuests()
}

const delayedSearch = _debounce(async () => {
	fetchParam.value.page = 1
	await fetchGuests()
}, 500)

const refresh = e => {
	mainStore.softReload()
	return e
}

const handleAfterSubmit = async () => {
	await fetchGuests()
	showForm.value = false
	showOnSiteForm.value = false
	showGiftForm.value = false
}

const handleReload = async () => {
	reloadLoading.value = true
	await fetchAggregate()
	await fetchGuests()
	reloadLoading.value = false
}

onMounted(async () => {
	await fetchAggregate()
	await fetchGuests()
})
</script>

<style scoped lang="scss">
.n-card {
	.card-wrap {
		width: 100%;

		.title {
			font-size: 18px;
			word-break: initial;
		}
		.value {
			font-family: var(--font-family-display);
			font-size: 22px;
			font-weight: bold;
			margin-bottom: 6px;
		}
	}
}
.icon {
	color: var(--secondary4-color);
	width: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;

	&.boxed {
		height: 50px;

		.bg {
			background-color: var(--secondary4-color);
			opacity: 0.1;
			position: absolute;
			top: 0;
			left: 0;
			border-radius: 50%;
			width: 100%;
			height: 100%;
		}
	}
}
</style>
