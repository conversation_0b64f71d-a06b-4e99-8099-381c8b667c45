<template>
	<div class="gallery-grid">
		<div v-if="loading" class="w-full flex justify-center items-center py-12">
			<n-spin size="large" />
		</div>

		<div v-else-if="error" class="w-full flex justify-center items-center py-12">
			<n-result status="error" title="Failed to load gallery" :description="error.toString()">
				<template #footer>
					<n-button @click="$emit('reload')">Try Again</n-button>
				</template>
			</n-result>
		</div>

		<div v-else-if="!items || items.length === 0" class="w-full flex justify-center items-center py-12">
			<n-result status="info" title="Tidak Ada Foto" description="Belum ada foto selfie tamu di acara ini.">
				<template #footer>
					<n-button v-if="showUploadButton" @click="$emit('upload')">Upload Images</n-button>
				</template>
			</n-result>
		</div>

		<div v-else class="masonry-grid">
			<GalleryItem v-for="item in items" :key="item.id" :item="item" @click="$emit('item-click', item)" />
		</div>

		<div v-if="meta && meta.filter_count > 0" class="pagination-container mt-6 flex justify-center">
			<n-pagination
				v-model:page="currentPage"
				:page-count="totalPages"
				:page-slot="5"
				:page-sizes="[12, 24, 48, 96]"
				v-model:page-size="pageSize"
				show-size-picker
				@update:page="$emit('page-change', $event)"
				@update:page-size="$emit('page-size-change', $event)"
			/>
		</div>
	</div>
</template>

<script setup>
import { computed, ref, watch } from "vue"
import { NSpin, NResult, NButton, NPagination } from "naive-ui"
import GalleryItem from "./GalleryItem.vue"

const props = defineProps({
	items: {
		type: Array,
		default: () => []
	},
	meta: {
		type: Object,
		default: null
	},
	loading: {
		type: Boolean,
		default: false
	},
	error: {
		type: [Error, String, Object],
		default: null
	},
	showUploadButton: {
		type: Boolean,
		default: true
	},
	initialPage: {
		type: Number,
		default: 1
	},
	initialPageSize: {
		type: Number,
		default: 24
	}
})

const emit = defineEmits(["item-click", "reload", "upload", "page-change", "page-size-change"])

// Pagination state
const currentPage = ref(props.initialPage)
const pageSize = ref(props.initialPageSize)

// Calculate total pages
const totalPages = computed(() => {
	if (!props.meta || !props.meta.filter_count) return 1
	return Math.ceil(props.meta.filter_count / pageSize.value)
})

// Watch for external page changes
watch(
	() => props.initialPage,
	newVal => {
		currentPage.value = newVal
	}
)

// Watch for external page size changes
watch(
	() => props.initialPageSize,
	newVal => {
		pageSize.value = newVal
	}
)
</script>

<style scoped>
.masonry-grid {
	display: grid;
	grid-template-columns: repeat(1, 1fr);
	gap: 1rem;
}

@media (min-width: 640px) {
	.masonry-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (min-width: 768px) {
	.masonry-grid {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media (min-width: 1024px) {
	.masonry-grid {
		grid-template-columns: repeat(4, 1fr);
	}
}

@media (min-width: 1280px) {
	.masonry-grid {
		grid-template-columns: repeat(5, 1fr);
	}
}

/* Make sure all items are the same height */
.gallery-item {
	height: 100%;
	width: 100%;
}
</style>
