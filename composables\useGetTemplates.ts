import { type DirectusQueryParams, type DirectusItems } from "nuxt-directus/dist/runtime/types/index"

import { type Template } from "~/types/globals"
const { getItems } = useDirectusItems()
export const useGetTemplates = () => {
	const data = ref(null)
	const meta = ref(null)
	const loading = ref(false)

	const load = async (fetchParams: DirectusQueryParams) => {
		loading.value = true
		const items: DirectusItems = await getItems<Template>({
			collection: "template",
			params: {
				...fetchParams,
				meta: "*"
			}
		})
		data.value = items.data
		meta.value = items.meta
		loading.value = false
	}

	return { data, meta, load, loading }
}
