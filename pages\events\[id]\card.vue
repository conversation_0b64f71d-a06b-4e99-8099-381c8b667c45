<template>
	<div class="page">
		<!-- header -->
		<n-h1 prefix="bar" class="mb-0">
			<h1 class="apply-font">KARTU UNDANGAN</h1>
		</n-h1>
		<n-text v-if="aggregate" class="ml-5" depth="3">
			{{ aggregate.data[0].count.id }} undangan dan {{ aggregate.data[0].sum.amount_guest }} pax ditemukan
		</n-text>
		<n-space vertical size="large" class="mt-5">
			<!-- Modal for cropping -->
			<n-modal v-model:show="showModal" preset="card" title="Sesuaikan Foto">
				<n-scrollbar style="max-height: 70vh">
					<Cropper
						v-if="imageDataUrl"
						ref="cropper"
						:src="imageDataUrl"
						:stencil-props="{ aspectRatio: 6 / 4 }"
						@change="onCropChange"
					/>
				</n-scrollbar>
				<template #footer>
					<n-flex justify="end">
						<n-button type="primary" @click="confirmCrop">Potong</n-button>
						<n-button @click="cancelCrop">Batalkan</n-button>
					</n-flex>
				</template>
			</n-modal>

			<n-modal v-model:show="showModal2" preset="card" title="Sesuaikan Foto">
				<n-scrollbar style="max-height: 70vh">
					<Cropper
						v-if="imageDataUrl2"
						ref="cropper2"
						:src="imageDataUrl2"
						:stencil-props="{ aspectRatio: 60 / 98 }"
						@change="onCropChange2"
					/>
				</n-scrollbar>
				<template #footer>
					<n-flex justify="end">
						<n-button type="primary" @click="confirmCrop2">Potong</n-button>
						<n-button @click="cancelCrop2">Batalkan</n-button>
					</n-flex>
				</template>
			</n-modal>

			<n-modal v-model:show="modalGenerateCard" preset="card" title="Generate Kartu" style="width: auto">
				<n-flex justify="center" align="center" vertical>
					<n-progress type="circle" color="#ff4dc2" status="info" :percentage="percentageGenerate" />
					<h3 class="mt-8">Please Wait</h3>
				</n-flex>
			</n-modal>

			<n-row :gutter="[12, 12]">
				<n-col :span="viewport.isLessThan('desktop') ? 24 : 12">
					<n-h2 class="mb-4">
						<h2>Data Kartu</h2>
					</n-h2>
					<div>
						<n-card>
							<n-form>
								<n-form-item v-if="tabPane == 'DenganFoto'" label="Foto">
									<!-- Button to select image -->
									<n-button @click="selectImage">Choose Image</n-button>
									<input
										id="file-1"
										type="file"
										ref="fileInput"
										accept="image/*"
										@change="onFileChange"
										style="display: none"
									/>
								</n-form-item>
								<n-form-item v-if="tabPane == 'Kostum'" label="Background">
									<!-- Button to select image -->
									<n-button @click="selectImage2">Choose Image</n-button>
									<input
										id="file-2"
										type="file"
										ref="fileInput"
										accept="image/*"
										@change="onFileChange2"
										style="display: none"
									/>
								</n-form-item>
								<n-form-item label="Font Nama Mempelai">
									<UniversalFontPicker v-model="cardInputModel.nameFont" :min="1" clearable />
								</n-form-item>
								<n-form-item label="Font Teks">
									<UniversalFontPicker v-model="cardInputModel.textFont" :min="1" clearable />
								</n-form-item>
								<n-form-item label="Bahasa">
									<n-select
										v-model:value="cardInputModel.language"
										:options="[
											{ label: 'Bahasa Indonesia', value: 0 },
											{ label: 'English', value: 1 }
										]"
									/>
								</n-form-item>
								<n-form-item label="Nama Mempelai">
									<n-input v-model:value="cardInputModel.name" />
								</n-form-item>
								<n-form-item label="Judul">
									<n-input v-model:value="cardInputModel.title" />
								</n-form-item>
								<n-form-item label="Tanggal">
									<n-input v-model:value="cardInputModel.date" />
								</n-form-item>
								<n-form-item label="Sesi">
									<n-select
										v-model:value="cardInputModel.session"
										:options="[
											{ label: 'Sesi Sesuai Input Waktu', value: 0 },
											{ label: 'Sesuai Sesi Tamu', value: 1 }
										]"
									/>
								</n-form-item>
								<n-form-item v-if="cardInputModel.session == 0" label="Waktu">
									<n-input v-model:value="cardInputModel.time" />
								</n-form-item>
								<n-form-item label="Tempat">
									<n-input v-model:value="cardInputModel.location" />
								</n-form-item>
								<n-form-item label="Himbauan">
									<n-input v-model:value="cardInputModel.info" type="textarea" />
								</n-form-item>
								<n-form-item label="Warna text">
									<n-color-picker
										v-model:value="cardInputModel.textColor"
										:modes="['hex', 'rgb', 'hsl']"
										:show-alpha="false"
									/>
								</n-form-item>
								<n-form-item v-if="tabPane != 'Kostum'" label="Warna background">
									<n-color-picker
										v-model:value="cardInputModel.backgroundColor"
										:modes="['hex', 'rgb', 'hsl']"
										:show-alpha="false"
									/>
								</n-form-item>
								<n-form-item label="Warna text mempelai">
									<n-color-picker
										v-model:value="cardInputModel.nameColor"
										:modes="['hex', 'rgb', 'hsl']"
										:show-alpha="false"
									/>
								</n-form-item>
								<n-form-item label="Warna text judul">
									<n-color-picker
										v-model:value="cardInputModel.titleColor"
										:modes="['hex', 'rgb', 'hsl']"
										:show-alpha="false"
									/>
								</n-form-item>
								<n-form-item v-if="tabPane == 'DenganFoto'" label="Warna background judul">
									<n-color-picker
										v-model:value="cardInputModel.titleBgColor"
										:modes="['hex', 'rgb', 'hsl']"
										:show-alpha="false"
									/>
								</n-form-item>
							</n-form>
							<n-button :loading="loadingSaveCard" type="primary" @click="saveCard" style="width: 100%">
								Simpan
							</n-button>
						</n-card>
					</div>
				</n-col>
				<n-col :span="viewport.isLessThan('desktop') ? 24 : 12">
					<n-h2 class="mb-4">
						<h2>Preview Kartu</h2>
					</n-h2>
					<div>
						<n-tabs v-model:value="tabPane" type="segment" animated>
							<n-tab-pane name="DenganFoto" tab="Dengan Foto" class="flex justify-center items-center">
								<CardQrPhoto
									:card-input-model="cardInputModel"
									:url-photo="urlPhoto"
									name="Nama Tamu"
									qr="text"
								/>
							</n-tab-pane>
							<n-tab-pane name="Kostum" tab="Kustom" class="flex justify-center items-center">
								<CardQrCustom
									:card-input-model="cardInputModel"
									:url-b-g="urlBG"
									name="Nama Tamu"
									qr="text"
								/>
							</n-tab-pane>
							<!--
							<n-tab-pane name="TanpaFoto" tab="Tanpa Foto" class="flex justify-center items-center">
								<CardQrColor :card-input-model="cardInputModel" name="Nama Tamu" qr="text" />
							</n-tab-pane>
							-->
						</n-tabs>
					</div>
				</n-col>
			</n-row>

			<!-- guest data related -->
			<n-h2 class="mb-0">
				<h2>Pilih Undangan</h2>
			</n-h2>
			<template v-if="guests">
				<!-- search -->
				<n-space align="center" justify="space-between">
					<n-input v-model:value="search" @input="searching" placeholder="Cari Tamu" clearable />
					<n-button circle type="primary" :ghost="!showOptions" @click="handleOptionsShow">
						<template #icon>
							<Icon :icon="SettingIcon" width="24" />
						</template>
					</n-button>
				</n-space>

				<n-space v-if="checkedRowKeys.length > 0" align="center">
					<n-text>{{ checkedRowKeys.length }} data telah dipilih</n-text>
					<n-button @click="handleClearChecked()">
						<template #icon>
							<Icon :icon="ClearIcon" width="24" />
						</template>
						Kosongkan
					</n-button>
					<n-button :loading="downloadLoading" @click="generateImagesAndHTML()">
						<template #icon>
							<Icon :icon="DownloadIcon" width="24" />
						</template>
						download
					</n-button>
				</n-space>

				<!-- filter -->
				<n-collapse-transition :show="showOptions">
					<n-card>
						<n-form>
							<n-form-item label="Tampilkan Kolom">
								<n-select
									v-model:value="selectedColumns"
									:options="columnOptions"
									placeholder="Pilih Kolom untuk Ditampilkan"
									multiple
								/>
							</n-form-item>
							<n-space align="end">
								<n-form-item label="Filter Level">
									<n-select
										v-model:value="filterModel.level"
										:options="optionsLevelExtend"
										:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
									/>
								</n-form-item>
								<n-form-item>
									<template #label>
										Filter Kategori
										<n-popover placement="bottom-end" trigger="hover">
											<template #trigger>
												<Icon
													class=""
													icon="ion:information-circle-outline"
													width="20"
													style="color: #00b27b; display: inline"
												/>
											</template>
											Filter ini digunakan untuk
											<br />
											menampilkan tamu undangan
											<br />
											berdasarkan kategori
											<br />
											yang sudah diinput
											<br />
											(Ex: Tamu Ayah, Tamu UGM, dll)
										</n-popover>
									</template>
									<n-select
										v-model:value="filterModel.category"
										placeholder="Filter kategori"
										:options="optionsCategory"
										:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
									/>
								</n-form-item>
								<n-form-item label="Filter Sesi">
									<n-select
										v-model:value="filterModel.shift"
										placeholder="Filter Sesi"
										:options="optionsShift"
										:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
									/>
								</n-form-item>
								<n-form-item label="Filter RSVP">
									<n-select
										v-model:value="filterModel.rsvp"
										placeholder="Filter RSVP"
										:options="optionsRsvp"
										:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
									/>
								</n-form-item>
								<n-form-item label="Filter Jenis Undangan">
									<n-select
										v-model:value="filterModel.typeInvitation"
										placeholder="Filter Jenis Undangan"
										:options="optionsTypeInvitation"
										:style="viewport.isLessThan('tablet') ? 'width: 100vw' : 'width: 180px'"
									/>
								</n-form-item>
							</n-space>
							<n-form-item>
								<n-space>
									<n-button @click="resetFilter">Reset</n-button>
									<n-button type="primary" @click="applyFilter">Terapkan</n-button>
								</n-space>
							</n-form-item>
						</n-form>
					</n-card>
				</n-collapse-transition>

				<!-- table -->
				<n-data-table
					size="small"
					row-class-name="text-sm"
					:loading="statusGuests === 'pending'"
					:columns="visibleColumns"
					:data="guests.data"
					:scroll-x="scrollX"
					:row-key="rowKey"
					:checked-row-keys="checkedRowKeys"
					:row-props="rowProps"
					@update:checked-row-keys="handleCheck"
				/>
				<n-space align="center">
					<n-pagination
						v-model:page="params.page"
						:page-count="totalPages"
						:page-slot="7"
						@update:page="refreshTable"
					/>
					<n-select v-model:value="params.limit" :options="optionsRowPerPage" @update:value="refreshTable" />
				</n-space>
			</template>
			<!-- skeleton -->
			<TableSkeleton v-else />

			<div
				v-if="listCheckedGuest"
				ref="generatedContainer"
				style="position: absolute; width: 700px; top: -9999px; left: -9999px"
			>
				<template v-if="tabPane == 'DenganFoto' && guestCard">
					<CardQrPhoto
						:id="`guest-card`"
						:key="guestKey"
						:card-input-model="cardInputModel"
						:url-photo="urlPhoto"
						:name="guestCard.name"
						:qr="guestCard.code_guest"
						:level="guestCard.level"
						:shift="guestCard.shift"
					/>
				</template>
				<template v-if="tabPane == 'Kostum' && guestCard">
					<CardQrCustom
						:id="`guest-card`"
						:key="guestKey"
						:card-input-model="cardInputModel"
						:url-b-g="urlBG"
						:name="guestCard.name"
						:qr="guestCard.code_guest"
						:level="guestCard.level"
						:shift="guestCard.shift"
					/>
				</template>
			</div>
		</n-space>
	</div>
</template>

<script setup>
import {
	NH1,
	NH2,
	NText,
	NSpace,
	NFlex,
	NDataTable,
	NInput,
	NButton,
	NSelect,
	NCollapseTransition,
	NCard,
	NPagination,
	NForm,
	NFormItem,
	NPopover,
	NTabs,
	NTabPane,
	NColorPicker,
	NModal,
	NProgress
} from "naive-ui"
import TableRowName from "~/components/app/table/TableRowName.vue"
import TableRowRsvp from "~/components/app/table/TableRowRsvp.vue"
import JSZip from "jszip"
import { saveAs } from "file-saver"
import { toJpeg } from "html-to-image"
import UniversalFontPicker from "@formester/universal-font-picker"
import "@formester/universal-font-picker/dist/universal-font-picker.css"
import { Cropper } from "vue-advanced-cropper"
import "vue-advanced-cropper/dist/style.css"
import { Icon } from "@iconify/vue"

definePageMeta({
	name: "Card",
	title: "Card"
})

const SettingIcon = "ion:filter"
const ClearIcon = "mdi:clear-circle-outline"
const DownloadIcon = "material-symbols:download"

const config = useRuntimeConfig()
const { token } = useDirectusToken()
const route = useRoute()
const viewport = useViewport()
const message = useMessage()

const tabPane = ref("DenganFoto")

const urlPhoto = ref("")
const urlBG = ref("")

const { data: agenda } = await useAsyncData(
	"agenda",
	async () =>
		$fetch(`${config.public.directusUrl}/items/event/${route.params.id}?fields[]=*&fields[]=card.*.*`, {
			headers: {
				Authorization: `Bearer ${token.value}`
			}
		}),
	{
		server: false
	}
)

const params = ref({
	search: null,
	page: 1,
	limit: 25,
	filter: {
		event: {
			_eq: route.params.id
		}
	}
})

const {
	data: aggregate,
	status: statusAggregate,
	refresh: refreshAggregate
} = await useAsyncData(
	"aggregate-card",
	async () =>
		$fetch(`${config.public.directusUrl}/items/guest`, {
			headers: {
				Authorization: `Bearer ${token.value}`
			},
			params: {
				...params.value,
				page: 1,
				limit: 1000,
				aggregate: {
					count: "id",
					sum: "amount_guest"
				}
			}
		}),
	{
		server: false
	}
)

const {
	data: guests,
	status: statusGuests,
	refresh: refreshGuest
} = await useAsyncData(
	"guests-card",
	async () =>
		$fetch(`${config.public.directusUrl}/items/guest`, {
			headers: {
				Authorization: `Bearer ${token.value}`
			},
			params: {
				...params.value,
				fields: [
					"id",
					"name",
					"status_relation",
					"code_guest",
					"level",
					"shift",
					"table",
					"category",
					"address",
					"phone",
					"amount_guest",
					"amount_souvenir",
					"note",
					"rsvp"
				]
			}
		}),
	{
		server: false
	}
)

const search = ref("")

const searching = useDebounceFn(async () => {
	params.value.search = search.value
	params.value.page = 1
	await refreshTable()
}, 500)

const refreshTable = async () => {
	await refreshGuest()
	await refreshAggregate()
}

const columns = [
	{
		type: "selection",
		width: 30
	},
	{
		title: "Nama",
		key: "name",
		width: 200,
		render(row) {
			return h(TableRowName, {
				guest: row
			})
		}
	},
	{
		title: "Kode",
		key: "code_guest",
		width: 150,
		ellipsis: {
			tooltip: true
		}
	},
	{
		title: "Sesi",
		key: "shift",
		width: 150,
		ellipsis: {
			tooltip: true
		}
	},
	{
		title: "Meja",
		key: "table",
		width: 100,
		ellipsis: {
			tooltip: true
		}
	},
	{
		title: "Kategori",
		key: "category",
		width: 100,
		ellipsis: {
			tooltip: true
		}
	},
	{
		title: "Alamat",
		key: "address",
		width: 200,
		ellipsis: {
			tooltip: true
		}
	},
	{
		title: "No. HP",
		key: "phone",
		width: 150,
		ellipsis: {
			tooltip: true
		}
	},

	{
		title: "Pax",
		key: "amount_guest",
		width: 90,
		ellipsis: {
			tooltip: true
		}
	},
	{
		title: "Catatan",
		key: "note",
		width: 100,
		ellipsis: {
			tooltip: true
		}
	},
	{
		title: "RSVP",
		key: "rsvp",
		width: 90,
		render(row) {
			return h(TableRowRsvp, {
				guest: row
			})
		}
	},
	{
		title: "Jenis Undangan",
		key: "type_invitation",
		width: 150,
		ellipsis: {
			tooltip: true
		}
	}
]

const columnOptions = [
	{ label: "Nama", value: "name", disabled: true },
	{ label: "Kode", value: "code_guest" },
	{ label: "Sesi", value: "shift" },
	{ label: "Meja", value: "table" },
	{ label: "Kategori", value: "category" },
	{ label: "Alamat", value: "address" },
	{ label: "No HP", value: "phone" },
	{ label: "Pax", value: "amount_guest" },
	{ label: "Catatan", value: "note" },
	{ label: "RSVP", value: "rsvp" },
	{ label: "Jenis Undangan", value: "type_invitation" }
	// { label: "Aksi", value: "actions", disabled: true }
]

const selectedColumns = ref(["name", "code_guest"]) // Default selected columns

const visibleColumns = computed(() => {
	return columns.filter(col => selectedColumns.value.includes(col.key) || col.type === "selection")
})

const scrollX = computed(() => {
	return visibleColumns.value.reduce((a, b) => a + b.width, 0) + columns[0].width
})

const totalPages = computed(() => {
	return Math.ceil(aggregate.value.data[0].count.id / params.value.limit)
})

const rowKey = row => row.id

const checkedRowKeys = ref([])
const listCheckedGuest = ref([])
const listGenerateCard = ref([])
const downloadLoading = ref(false)

const rowProps = row => {
	return {
		/* onClick: () => {
			if (useIncludes(checkedRowKeys.value, row.id)) {
				usePull(checkedRowKeys.value, row.id)
			} else {
				checkedRowKeys.value.push(row.id)
			}
		} */
	}
}

const handleCheck = rowKeys => {
	checkedRowKeys.value = rowKeys

	// Remove objects in listCheckedGuest that are not in checkedRowKeys
	listCheckedGuest.value = listCheckedGuest.value.filter(guest => checkedRowKeys.value.includes(guest.id))

	// Add objects from guests to listCheckedGuest if their id is in checkedRowKeys but not in listCheckedGuest
	checkedRowKeys.value.forEach(id => {
		if (!listCheckedGuest.value.some(guest => guest.id === id)) {
			const guestToAdd = guests.value.data.find(guest => guest.id === id)
			if (guestToAdd) {
				listCheckedGuest.value.push(guestToAdd)
			}
		}
	})
}

const modalGenerateCard = ref(false)
const percentageGenerate = ref(0)
const guestCard = ref(null)
const guestKey = ref(0)

const generateImagesAndHTML = async () => {
	modalGenerateCard.value = true
	downloadLoading.value = true
	listGenerateCard.value = [...listCheckedGuest.value]
	listGenerateCard.value.sort((a, b) => a.id - b.id)

	const zip = new JSZip() // Initialize the ZIP file container
	const imagesFolder = zip.folder("images") // Folder for images

	const unit = 100 / listGenerateCard.value.length

	for (const guest of listGenerateCard.value) {
		guestCard.value = { ...guest }
		const elementId = `guest-card`
		guestKey.value += 1 // Increment key to force rerender
		await new Promise(resolve => setTimeout(resolve, 100)) // Small delay to allow DOM update

		const element = document.getElementById(elementId)

		if (element) {
			try {
				// Generate JPEG image from the guest card
				const dataUrl = await toJpeg(element, { quality: 1 })

				// Add image to the ZIP
				const imgData = dataUrl.replace(/^data:image\/jpeg;base64,/, "")
				imagesFolder.file(`${guest.code_guest}_${guest.name.replace(/[\s.,/]+/g, "_")}.jpg`, imgData, {
					base64: true
				})
			} catch (error) {
				console.error("Error generating image:", error)
			}
		}
		percentageGenerate.value += unit
	}

	// Generate the ZIP and trigger download
	zip.generateAsync({ type: "blob" }).then(blob => {
		saveAs(blob, "qr-card.zip")
	})

	listGenerateCard.value = []
	downloadLoading.value = false
	modalGenerateCard.value = false
	percentageGenerate.value = 0
}

const showOptions = ref(false)

const filterModel = ref({
	level: "",
	category: "",
	shift: "",
	rsvp: "",
	typeInvitation: ""
})

const optionsLevelExtend = [
	{
		label: "Semua",
		value: ""
	},
	...optionsLevel
]

const optionsCategory = ref([])

const getOptionsCategory = async () => {
	const dataCategory = await $fetch(`${config.public.directusUrl}/items/guest`, {
		headers: {
			Authorization: `Bearer ${token.value}`
		},
		params: {
			filter: {
				event: {
					_eq: route.params.id
				}
			},
			aggregate: {
				count: "id"
			},
			groupBy: ["category"]
		}
	})

	optionsCategory.value = dataCategory.data.map(item => {
		return {
			label: item.category,
			value: item.category
		}
	})

	optionsCategory.value.unshift({
		label: "Semua",
		value: ""
	})

	optionsCategory.value = optionsCategory.value.filter(item => item.value !== null)
}

const optionsShift = ref([])

const getOptionsShift = async () => {
	const dataShift = await $fetch(`${config.public.directusUrl}/items/guest`, {
		headers: {
			Authorization: `Bearer ${token.value}`
		},
		params: {
			filter: {
				event: {
					_eq: route.params.id
				}
			},
			aggregate: {
				count: "id"
			},
			groupBy: ["shift"]
		}
	})
	optionsShift.value = dataShift.data.map(item => {
		return {
			label: item.shift,
			value: item.shift
		}
	})

	optionsShift.value.unshift({
		label: "Semua",
		value: ""
	})

	optionsShift.value = optionsShift.value.filter(item => item.value !== null)
}

const optionsTypeInvitation = ref([])

const getOptionsTypeInvitation = async () => {
	const dataTpeInvitation = await $fetch(`${config.public.directusUrl}/items/guest`, {
		headers: {
			Authorization: `Bearer ${token.value}`
		},
		params: {
			filter: {
				event: {
					_eq: route.params.id
				}
			},
			aggregate: {
				count: "id"
			},
			groupBy: ["type_invitation"]
		}
	})
	optionsTypeInvitation.value = dataTpeInvitation.data.map(item => {
		return {
			label: item.type_invitation,
			value: item.type_invitation
		}
	})

	optionsTypeInvitation.value.unshift({
		label: "Semua",
		value: ""
	})

	optionsTypeInvitation.value = optionsTypeInvitation.value.filter(item => item.value !== null)
}

const applyFilter = async () => {
	// Create base filter
	const baseFilter = {
		_and: [
			{
				event: {
					_eq: route.params.id
				}
			}
		]
	}

	// Add conditions if input values are present
	if (filterModel.value.level) {
		baseFilter._and.push({
			level: {
				_eq: filterModel.value.level
			}
		})
	}

	if (filterModel.value.category) {
		baseFilter._and.push({
			category: {
				_contains: filterModel.value.category
			}
		})
	}

	if (filterModel.value.shift) {
		baseFilter._and.push({
			shift: {
				_contains: filterModel.value.shift
			}
		})
	}

	if (filterModel.value.rsvp) {
		if (filterModel.value.rsvp === "not yet") {
			baseFilter._and.push({
				rsvp: {
					_eq: null
				}
			})
		} else {
			baseFilter._and.push({
				rsvp: {
					_eq: filterModel.value.rsvp
				}
			})
		}
	}

	if (filterModel.value.typeInvitation) {
		baseFilter._and.push({
			type_invitation: {
				_contains: filterModel.value.typeInvitation
			}
		})
	}

	params.value.filter = baseFilter

	await refreshTable()
}

const resetFilter = async () => {
	filterModel.value = {
		level: "",
		category: "",
		shift: "",
		rsvp: ""
	}

	await applyFilter()
}

const optionsRsvp = ref([
	{ label: "Semua", value: "" },
	{ label: "Not yet", value: "not yet" },
	{ label: "Not going", value: "0" },
	{ label: "Going", value: "1" }
])

const handleOptionsShow = async () => {
	// fetch when hide to show
	if (!showOptions.value) {
		await getOptionsCategory()
		await getOptionsShift()
		await getOptionsTypeInvitation()
	}
	showOptions.value = !showOptions.value
}

const handleClearChecked = () => {
	checkedRowKeys.value = []
	listCheckedGuest.value = []
}

const captureCardFoto = ref(null)

const downloadImage = async () => {
	if (captureCardFoto.value) {
		try {
			const dataUrl = await toJpeg(captureCardFoto.value)
			const link = document.createElement("a")
			link.href = dataUrl
			link.download = "downloaded-image.jpeg"
			link.click()
		} catch (error) {
			console.error("Failed to capture image", error)
		}
	}
}

const cardInputModel = ref({
	name: agenda.value.data.title,
	title: "Kartu Akses",
	date: dayjs(agenda.value.data.start).locale("id").format("dddd, D MMMM YYYY"),
	time: dayjs(agenda.value.data.start).locale("id").format("HH:mm") + " WIB",
	place: agenda.value.data.location,
	info: "Setiap tamu undangan yang hadir mohon membawa QR-Code ini sebagai pengganti buku tamu",
	nameFont: "Abril Fatface",
	textFont: "Public Sans",
	textColor: "#000000",
	backgroundColor: "#ffffff",
	titleColor: "#ffffff",
	titleBgColor: "#ff61c9",
	nameColor: "#ffffff",
	language: 0,
	session: 0
})

const loadingSaveCard = ref(false)

const saveCard = async () => {
	loadingSaveCard.value = true
	try {
		let cardData = null
		if (agenda.value.data.card) cardData = agenda.value.data.card

		// card photo
		let cardPhotoId = null
		if (canvasCropper.value) {
			// Convert the cropped image to a Blob
			const blob = await new Promise(resolve => canvasCropper.value.toBlob(resolve, "image/jpeg"))
			console.log("Cropped image size:", blob.size, "bytes")
			// Upload cropped image to Directus

			const follderData = await $fetch(
				`${config.public.directusUrl}/folders/e7adb8ad-c7b4-49f6-8ffa-cf88bc9566b3`,
				{
					headers: {
						Authorization: `Bearer ${token.value}`
					}
				}
			)

			// Upload the file and assign it to the folder
			const formData = new FormData()
			formData.append("folder", follderData.data.id) // Assign file to folder
			formData.append("file", blob, `card-${agenda.value.data.id}.jpeg`)

			if (cardData && cardData.photo) {
				cardPhotoId = cardData.photo.id
				await $fetch(`/files/${cardPhotoId}`, {
					baseURL: config.public.directusUrl,
					method: "PATCH",
					headers: {
						Authorization: `Bearer ${token.value}`
					},
					body: formData
				})
			} else {
				const result = await $fetch(`/files`, {
					baseURL: config.public.directusUrl,
					method: "POST",
					headers: {
						Authorization: `Bearer ${token.value}`
					},
					body: formData
				})
				cardPhotoId = result.data.id
			}
		} else {
			if (cardData && cardData.photo) cardPhotoId = cardData.photo
		}

		// card background
		let cardBackgroundId = null
		if (canvasCropper2.value) {
			// Convert the cropped image to a Blob
			const blob = await new Promise(resolve => canvasCropper2.value.toBlob(resolve, "image/jpeg"))
			console.log("Cropped image size:", blob.size, "bytes")
			// Upload cropped image to Directus

			const follderData = await $fetch(
				`${config.public.directusUrl}/folders/e7adb8ad-c7b4-49f6-8ffa-cf88bc9566b3`,
				{
					headers: {
						Authorization: `Bearer ${token.value}`
					}
				}
			)

			// Upload the file and assign it to the folder
			const formData = new FormData()
			formData.append("folder", follderData.data.id) // Assign file to folder
			formData.append("file", blob, `background-${agenda.value.data.id}.jpeg`)

			if (cardData && cardData.card_pic) {
				cardBackgroundId = cardData.card_pic.id
				await $fetch(`/files/${cardBackgroundId}`, {
					baseURL: config.public.directusUrl,
					method: "PATCH",
					headers: {
						Authorization: `Bearer ${token.value}`
					},
					body: formData
				})
			} else {
				const result = await $fetch(`/files`, {
					baseURL: config.public.directusUrl,
					method: "POST",
					headers: {
						Authorization: `Bearer ${token.value}`
					},
					body: formData
				})

				cardBackgroundId = result.data.id
			}
		} else {
			if (cardData && cardData.card_pic) cardBackgroundId = cardData.card_pic
		}

		// save card
		const cardBody = {
			photo: cardPhotoId,
			card_pic: cardBackgroundId,
			name_font: cardInputModel.value.nameFont,
			text_font: cardInputModel.value.textFont,
			name: cardInputModel.value.name,
			title: cardInputModel.value.title,
			date: cardInputModel.value.date,
			time: cardInputModel.value.time,
			place: cardInputModel.value.location,
			info: cardInputModel.value.info,
			color_text: cardInputModel.value.textColor,
			color_background: cardInputModel.value.backgroundColor,
			color_name: cardInputModel.value.nameColor,
			color_title: cardInputModel.value.titleColor,
			color_background_title: cardInputModel.value.titleBgColor,
			event: agenda.value.data.id,
			language: cardInputModel.value.language,
			session: cardInputModel.value.session
		}

		let idCard = null
		if (cardData) {
			idCard = cardData.id
			await $fetch(`/items/card/${idCard}`, {
				baseURL: config.public.directusUrl,
				method: "PATCH",
				headers: {
					Authorization: `Bearer ${token.value}`
				},
				body: cardBody
			})
		} else {
			const card = await $fetch(`/items/card`, {
				baseURL: config.public.directusUrl,
				method: "POST",
				headers: {
					Authorization: `Bearer ${token.value}`
				},
				body: cardBody
			})
			idCard = card.data.id
		}

		await $fetch(`/items/event/${agenda.value.data.id}`, {
			baseURL: config.public.directusUrl,
			method: "PATCH",
			headers: {
				Authorization: `Bearer ${token.value}`
			},
			body: {
				card: idCard
			}
		})

		message.success("berhasil tersimpan")
	} catch (error) {
		loadingSaveCard.value = true
		console.error("Error saving card:", error)
		message.error(`gagal menyimpan. ukuran file melebihi 1MB`)
	}
	loadingSaveCard.value = false
}

const showModal = ref(false)
const imageDataUrl = ref(null)
const cropData = ref(null)
const croppedImage = ref(null)
const cropper = ref(null)
const canvasCropper = ref(null)

const showModal2 = ref(false)
const imageDataUrl2 = ref(null)
const cropData2 = ref(null)
const croppedImage2 = ref(null)
const cropper2 = ref(null)
const canvasCropper2 = ref(null)

const selectImage = () => {
	const input = document.querySelector("#file-1")
	input.click()
}

const onFileChange = event => {
	const file = event.target.files[0]
	if (file) {
		const reader = new FileReader()
		reader.onload = e => {
			imageDataUrl.value = e.target.result
			showModal.value = true
		}
		reader.readAsDataURL(file)
	}
}

const onCropChange = ({ coordinates }) => {
	cropData.value = coordinates
}

const confirmCrop = () => {
	const { coordinates, canvas } = cropper.value.getResult()
	canvasCropper.value = canvas
	if (canvas) {
		croppedImage.value = canvas.toDataURL("image/png")
		urlPhoto.value = croppedImage.value
	}
	showModal.value = false
}

const cancelCrop = () => {
	showModal.value = false
	imageDataUrl.value = null
}

const selectImage2 = () => {
	const input = document.querySelector("#file-2")
	input.click()
}

const onFileChange2 = event => {
	const file = event.target.files[0]
	if (file) {
		const reader = new FileReader()
		reader.onload = e => {
			imageDataUrl2.value = e.target.result
			showModal2.value = true
		}
		reader.readAsDataURL(file)
	}
}

const onCropChange2 = ({ coordinates }) => {
	cropData2.value = coordinates
}

const confirmCrop2 = () => {
	const { coordinates, canvas } = cropper2.value.getResult()
	canvasCropper2.value = canvas
	if (canvas) {
		croppedImage2.value = canvas.toDataURL("image/png")
		urlBG.value = croppedImage2.value
	}
	showModal2.value = false
}

const cancelCrop2 = () => {
	showModal2.value = false
	imageDataUrl2.value = null
}

// const fetchCard = async () => {
// 	try {
// 		const { data } = await useFetch("/api/events/" + id.value + "/card", {
// 			method: "GET"
// 		})
// 		cardQrObject.value = data.value
// 	} catch (error) {
// 		console.error(error)
// 	}
// }

const cardDataFound = computed(() => {
	return agenda.value.data.card ? true : false
})

onMounted(() => {
	console.log("cardDataFound", cardDataFound.value)
	if (cardDataFound.value) {
		const cardServer = agenda.value.data.card
		cardInputModel.value = {
			name: cardServer.name,
			title: cardServer.title,
			date: cardServer.date,
			time: cardServer.time,
			location: cardServer.place,
			info: cardServer.info,
			nameFont: cardServer.name_font,
			textFont: cardServer.text_font,
			textColor: cardServer.color_text,
			backgroundColor: cardServer.color_background,
			nameColor: cardServer.color_name,
			titleColor: cardServer.color_title,
			titleBgColor: cardServer.color_background_title,
			language: cardServer.language,
			session: cardServer.session
		}
		if (cardServer.photo)
			urlPhoto.value = `${config.public.directusUrl}/assets/${cardServer.photo.id}/${cardServer.photo.filename_download}?date=${Date.now()}`
		if (cardServer.card_pic)
			urlBG.value = `${config.public.directusUrl}/assets/${cardServer.card_pic.id}/${cardServer.card_pic.filename_download}?date=${Date.now()}`
	}
})
</script>

<style lang="scss" scoped>
.qr-card {
	// width: calc(60px * 7);
	// height: calc(98px * 7);
	aspect-ratio: 60 / 98;
}

.image-card {
	// width: calc(60px * 7);
	// height: calc(40px * 7);
	// aspect-ratio: 60 / 40;
}

:deep(.n-color-picker-trigger) {
	border-radius: 0px;
}

:deep(.universal-font-picker) {
	background-color: white;
	color: black;
}
</style>
