<template>
	<div class="page">
		<n-h1 prefix="bar" class="mb-4">
			<h1>
				KIRIM PESAN

				<n-popover placement="bottom" trigger="hover">
					<template #trigger>
						<Icon class="mb-4" color="#00B27B" :size="20" name="ion:information-circle-outline" />
					</template>
					<ul style="list-style: none">
						<li>
							<n-checkbox class="mr-2" checked />
							tandai terkirim
						</li>
						<li>
							<Icon class="mr-2" :size="16" name="ion:copy-outline" />
							salin pesan
						</li>
						<li>
							<Icon class="mr-2" :size="16" name="logos:whatsapp-icon" />
							kirim pesan
						</li>
					</ul>
				</n-popover>
			</h1>
		</n-h1>
		<n-tabs type="segment" default-value="Tamu" animated>
			<n-tab-pane id="tamu-tab" name="Tamu" tab="Tamu">
				<SenderMessage :agenda="agendaData" />
			</n-tab-pane>
			<n-tab-pane id="template-tab" name="Template" tab="Template">
				<SenderTemplate :agenda="agendaData" />
			</n-tab-pane>
		</n-tabs>

		<!-- Floating Action Button -->
		<div id="fab-button" class="fab" @click="goToGuide">
			<Icon name="ion:book-outline" :size="24" color="white" />
		</div>

		<!-- Tour Modal -->
		<n-modal v-model:show="showTour" :mask-closable="false" preset="card" style="width: 90%; max-width: 500px">
			<template #header>
				<div class="flex items-center justify-between w-full">
					<span>{{ tourSteps[tourStep]?.title }}</span>
					<span class="text-sm opacity-60">{{ tourStep + 1 }}/{{ tourSteps.length }}</span>
				</div>
			</template>

			<div class="tour-content">
				<p>{{ tourSteps[tourStep]?.content }}</p>
			</div>

			<template #footer>
				<n-space justify="space-between">
					<n-button quaternary @click="skipTour">Lewati</n-button>
					<n-space>
						<n-button v-if="tourStep > 0" @click="tourStep--">Sebelumnya</n-button>
						<n-button type="primary" @click="nextTourStep">
							{{ tourStep === tourSteps.length - 1 ? "Selesai" : "Selanjutnya" }}
						</n-button>
					</n-space>
				</n-space>
			</template>
		</n-modal>
	</div>
</template>

<script setup>
import { NH1, NTabs, NTabPane, NPopover, NCheckbox, NModal, NButton, NSpace } from "naive-ui"

definePageMeta({
	name: "Sender",
	title: "Sender"
})

const route = useRoute()
const router = useRouter()

const { data: agendaData, loading: loadingAgenda, load: loadAgenda } = useGetAgendaById()

// Tour functionality
const showTour = ref(false)
const tourStep = ref(0)
const hasSeenTour = useLocalStorage(`sender-tour-${route.params.id}`, false)

const tourSteps = [
	{
		title: "Selamat Datang di Halaman Kirim Pesan!",
		content: "Mari kami tunjukkan cara menggunakan fitur-fitur di halaman ini.",
		target: null
	},
	{
		title: "Tab Tamu",
		content: "Di tab ini Anda dapat melihat daftar tamu dan mengirim pesan kepada mereka.",
		target: "tamu-tab"
	},
	{
		title: "Tab Template",
		content: "Di tab ini Anda dapat mengelola template pesan yang akan dikirim.",
		target: "template-tab"
	},
	{
		title: "Tombol Panduan",
		content: "Klik tombol buku ini kapan saja untuk melihat panduan lengkap penggunaan.",
		target: "fab-button"
	}
]

const fetchAgenda = async () => {
	try {
		await loadAgenda(route.params.id)
	} catch (e) {
		console.error(e)
	}
}

const goToGuide = async () => {
	// router.push(`/events/${route.params.id}/guide`)
	await navigateTo(`https://drive.google.com/file/d/1h2Od-lmM0QdrBfxSLOv_gjFmeoJ2GWC6/view?usp=sharing`, {
		external: true
	})
}

const startTour = () => {
	showTour.value = true
	tourStep.value = 0
}

const nextTourStep = () => {
	if (tourStep.value < tourSteps.length - 1) {
		tourStep.value++
	} else {
		closeTour()
	}
}

const closeTour = () => {
	showTour.value = false
	hasSeenTour.value = true
}

const skipTour = () => {
	closeTour()
}

onBeforeMount(async () => {
	await fetchAgenda()
})

onMounted(() => {
	// Show tour if user hasn't seen it before
	if (!hasSeenTour.value) {
		setTimeout(() => {
			startTour()
		}, 1000) // Delay to ensure page is fully loaded
	}
})
</script>

<style scoped>
@keyframes pulse-glow {
	0% {
		transform: scale(1);
		box-shadow:
			0 4px 12px rgba(0, 0, 0, 0.15),
			0 0 20px var(--primary-030-color, rgba(255, 97, 201, 0.3));
	}
	50% {
		transform: scale(1.05);
		box-shadow:
			0 6px 16px rgba(0, 0, 0, 0.2),
			0 0 40px var(--primary-050-color, rgba(255, 97, 201, 0.5)),
			0 0 60px var(--primary-030-color, rgba(255, 97, 201, 0.3));
	}
	100% {
		transform: scale(1);
		box-shadow:
			0 4px 12px rgba(0, 0, 0, 0.15),
			0 0 20px var(--primary-030-color, rgba(255, 97, 201, 0.3));
	}
}

.fab {
	position: fixed;
	bottom: 24px;
	right: 24px;
	width: 56px;
	height: 56px;
	background-color: var(--primary-color);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 1000;
	transition: transform 0.3s ease;
	animation: pulse-glow 1.5s ease-in-out infinite;
}

.fab:hover {
	transform: scale(1.1);
	animation-play-state: paused;
	box-shadow:
		0 6px 16px rgba(0, 0, 0, 0.2),
		0 0 40px var(--primary-060-color, rgba(255, 97, 201, 0.6)),
		0 0 60px var(--primary-040-color, rgba(255, 97, 201, 0.4));
}

.fab:active {
	transform: scale(0.95);
}

@media (max-width: 768px) {
	.fab {
		bottom: 20px;
		right: 20px;
		width: 48px;
		height: 48px;
	}
}

@media (prefers-reduced-motion: reduce) {
	.fab {
		animation: none;
	}
}

.tour-content {
	padding: 16px 0;
	line-height: 1.6;
}

.tour-content p {
	margin: 0;
	color: var(--text-color);
}
</style>
