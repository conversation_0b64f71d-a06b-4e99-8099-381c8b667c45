<template>
	<component
		:is="viewport.isLessThan('tablet') ? NDrawer : NModal"
		v-model:show="show"
		:class="viewport.isLessThan('tablet') ? 'form-background rounded-none' : ''"
		:width="viewport.isLessThan('tablet') ? '100%' : ''"
		:placement="viewport.isLessThan('tablet') ? 'right' : ''"
		@after-enter="handleAfterEnter"
	>
		<component
			:is="viewport.isLessThan('tablet') ? NDrawerContent : NCard"
			:title="viewport.isGreaterOrEquals('tablet') ? title : ''"
			:size="viewport.isGreaterOrEquals('tablet') ? 'medium' : ''"
			:closable="viewport.isGreaterOrEquals('tablet')"
			:style="{ maxWidth: viewport.isGreaterOrEquals('tablet') ? '600px' : '100%' }"
			@close="handleClose"
		>
			<template v-if="viewport.isLessThan('tablet')" #header>
				<n-page-header @back="handleClose">
					<template #title>
						{{ title }}
					</template>
				</n-page-header>
			</template>
			<div v-if="background" :style="{ background: `url(${background}) center center / cover no-repeat` }">
				<!-- Dark overlay -->
				<div
					:style="{
						backgroundColor: isThemeDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.5)'
					}"
					style="
						position: absolute;
						top: 0;
						left: 0;
						width: 100%;
						height: 100%;
						z-index: 1;
						pointer-events: none;
						border-radius: 16px;
					"
				></div>

				<!-- Content of the card -->
				<div style="position: relative; z-index: 2; overflow-y: auto; height: 100%; padding: 16px">
					<n-scrollbar :style="viewport.isGreaterOrEquals('tablet') ? 'max-height: 80vh' : ''">
						<slot></slot>
					</n-scrollbar>
				</div>
			</div>
			<template v-else>
				<slot></slot>
			</template>

			<template #footer>
				<slot name="footer"></slot>
			</template>
		</component>
	</component>
</template>

<script setup>
import { NDrawer, NModal, NDrawerContent, NCard, NPageHeader, NScrollbar } from "naive-ui"
import { useThemeStore } from "@/stores/theme"
defineProps({
	title: String,
	background: {
		type: String,
		default: ""
	}
})
const themeStore = useThemeStore()
const isThemeDark = computed(() => themeStore.isThemeDark)
const emit = defineEmits(["close", "after-enter"])
const handleClose = () => {
	emit("close")
}
const handleAfterEnter = () => {
	emit("after-enter")
}
const viewport = useViewport()
const show = defineModel()
</script>
