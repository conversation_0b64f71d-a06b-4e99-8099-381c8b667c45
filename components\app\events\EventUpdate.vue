<template>
	<CustomDrawer v-model:show="show" title="Edit Event" @close="handleClose" @after-enter="handleAfterEnter">
		<n-form ref="formRef" :rules="rules" :model="modelAgenda">
			<n-form-item path="code_event" label="Kode">
				<n-input v-model:value="modelAgenda.code_event" type="text" placeholder="masukkan kode event" />
			</n-form-item>
			<n-form-item path="title" label="judul">
				<n-input v-model:value="modelAgenda.title" type="text" placeholder="masukkan judul event" />
			</n-form-item>
			<n-form-item path="location" label="lokasi">
				<n-input v-model:value="modelAgenda.location" type="text" placeholder="masukkan lokasi event" />
			</n-form-item>
			<n-form-item path="startBeforeMapping" label="Waktu Event">
				<n-date-picker
					v-model:formatted-value="startBeforeMapping"
					value-format="yyyy-MM-dd HH:mm:ss"
					type="datetime"
					placeholder="masukkan tanggal acara"
					clearable
				/>
			</n-form-item>
			<n-form-item path="url_post" label="link">
				<n-input v-model:value="modelAgenda.url_post" type="text" placeholder="masukkan link event" />
			</n-form-item>
			<n-form-item path="feature" label="fitur">
				<n-checkbox-group v-model:value="features">
					<n-space item-style="display: flex;">
						<n-checkbox value="greeting_screen" label="Layar Sapa" />
						<n-checkbox value="print_feature" label="Cetak" />
						<n-checkbox value="photo" label="Selfie" />
					</n-space>
				</n-checkbox-group>
			</n-form-item>
			<n-form-item path="greeting_background" label="Background Layar Sapa">
				<n-input
					v-model:value="modelAgenda.greeting_background"
					type="text"
					placeholder="masukkan link background layar sapa"
				/>
			</n-form-item>
			<!-- scanner_pic -->
			<!-- cover -->
			<n-form-item label="Akses" path="userBeforeMapping">
				<n-select
					v-model:value="userBeforeMapping"
					multiple
					filterable
					placeholder="pilih user"
					:options="usersOptions"
					:loading="loadingUsers"
					clearable
					remote
					@search="handleSearch"
				/>
			</n-form-item>
		</n-form>
		<template #footer>
			<n-button block type="primary" :loading="loadingSubmit" @click="handleSubmitEvent">Edit Event</n-button>
		</template>
	</CustomDrawer>
</template>

<script setup>
import {
	NForm,
	NFormItem,
	NCheckbox,
	NSpace,
	NDatePicker,
	NCheckboxGroup,
	NInput,
	NSelect,
	NButton,
	useLoadingBar,
	useMessage
} from "naive-ui"
import _debounce from "lodash/debounce"
import _includes from "lodash/includes"
import _some from "lodash/some"

const { updateItem } = useDirectusItems()
const { getUsers } = useDirectusUsers()
const loadingBar = useLoadingBar()

const show = defineModel()

const emit = defineEmits(["close", "after-submit"])
const props = defineProps({
	agenda: {
		type: Object
	}
})

const modelAgenda = ref({
	code_event: null,
	title: null,
	location: null,
	start: null,
	url_post: null,
	greeting_screen: false,
	greeting_background: null,
	// cove: null,
	// scanner_pic: null,
	// cover: null,
	print_features: false,
	photo: false,
	users: [],
	feature: []
})
const features = ref([])
const usersData = ref([])
const usersOptions = ref([])
const loadingSubmit = ref(false)
const loadingUsers = ref(false)
const userBeforeMapping = ref([])
const startBeforeMapping = ref(null)
const message = useMessage()
const usersUpdate = {
	create: [],
	delete: []
}

const formRef = ref(null)
const rules = {
	code_event: {
		required: true,
		message: "kode Event tidak boleh kosong",
		trigger: ["input", "blur"]
	},
	title: {
		required: true,
		message: "Judul tidak boleh kosong",
		trigger: ["input", "blur"]
	}
}

const handleClose = () => {
	emit("close")
}

const handleSubmitEvent = e => {
	e.preventDefault()
	formRef.value?.validate(async errors => {
		if (!errors) {
			await submitEvent()
		} else {
			console.error(errors)
		}
	})
}

const mapingUpdateUsers = () => {
	// check delete
}

const submitEvent = async () => {
	loadingBar.start()
	try {
		// feature
		features.value.forEach(feature => {
			modelAgenda.value[feature] = true
		})
		// start
		if (startBeforeMapping.value) modelAgenda.value.start = toISOString(startBeforeMapping.value)

		// users
		/* props.agenda.users.forEach(user => {
			if (!_includes(userBeforeMapping.value, user.directus_users_id.id)) {
				usersUpdate.delete.push(user.id)
			}
		}) */
		const tempUser = []
		userBeforeMapping.value.forEach(user => {
			tempUser.push({
				directus_users_id: user
			})
		})

		modelAgenda.value.users = tempUser

		console.log(modelAgenda.value)
		const result = await updateItem({ id: props.agenda.id, collection: "event", item: modelAgenda.value })
		console.log(result)
		message.success("berhasil tersimpan")
		emit("after-submit")
		loadingBar.finish()
	} catch (e) {
		console.error(e)
		if (e.errors) {
			message.error(e.errors[0].message)
		} else message.error("gagal submit")
		loadingBar.error()
	}
}

const fetchUsers = async query => {
	try {
		const data = await getUsers({
			collection: "users",
			params: {
				search: query
			}
		})
		usersData.value = data
		usersOptions.value = []
		usersData.value.forEach(user => {
			usersOptions.value.push({
				label: `${user.first_name} ${user.last_name}`,
				value: user.id
			})
		})
	} catch (e) {
		console.error(e)
		if (e.errors) {
			message.error(e.errors[0].message)
		} else message.error("gagal mendapatkan daftar user")
	}
}

const delayedSearch = _debounce(async query => {
	loadingUsers.value = true
	usersOptions.value = []
	await fetchUsers(query)
	loadingUsers.value = false
}, 500)

const handleSearch = query => {
	if (!query.length) {
		usersOptions.value = []
		return
	}
	delayedSearch(query)
}

const handleAfterEnter = () => {
	const agendaTemp = { ...props.agenda }
	startBeforeMapping.value = props.agenda.start ? formatISODate(props.agenda.start) : null
	modelAgenda.value = agendaTemp
	features.value = []
	if (agendaTemp.greeting_screen) {
		features.value.push("greeting_screen")
	}

	if (agendaTemp.print_feature) {
		features.value.push("print_feature")
	}
	if (agendaTemp.photo) {
		features.value.push("photo")
	}
	userBeforeMapping.value = []
	agendaTemp.users.forEach(user => {
		usersOptions.value.push({
			label: `${user.directus_users_id.first_name} ${user.directus_users_id.last_name}`,
			value: user.directus_users_id.id
		})
		userBeforeMapping.value.push(user.directus_users_id.id)
	})
}
</script>

<style></style>
