// plugins/directus.ts
import { createDirectus, rest, graphql, authentication, realtime, readItem } from "@directus/sdk"

const directus = createDirectus("https://directus.bagimomen.my.id")
	.with(authentication())
	.with(graphql({ credentials: "include" }))
	.with(rest({ credentials: "include" }))

const directusRealtime = createDirectus("ws://directus.bagimomen.my.id/websocket")
	.with(authentication())
	.with(realtime())

export default defineNuxtPlugin(nuxtApp => {
	nuxtApp.provide("directus", directus)
	nuxtApp.provide("directusRealtime", directusRealtime)
})
